# 日志配置
# <AUTHOR> xiangjh

from loguru import logger
import os
from datetime import datetime, timedelta
import re

# 设置日志输出目录
LOG_DIR = os.path.join(os.getcwd(), "logs")
os.makedirs(LOG_DIR, exist_ok=True)

# 根据环境变量设置日志级别，默认为INFO
# 在生产环境中可以通过设置LOG_LEVEL=WARNING或ERROR来减少日志输出
LOG_LEVEL = os.getenv("LOG_LEVEL", "INFO")

# 存储logger的handlers ID，以便后续可以重新配置
logger_handlers = []

def clean_old_logs(log_dir=LOG_DIR, keep_days=7):
    """手动清理过期的日志文件"""
    try:
        now = datetime.now()
        cutoff_date = now - timedelta(days=keep_days)
        
        # 遍历日志目录中的所有文件
        for filename in os.listdir(log_dir):
            if filename.endswith(".log"):
                # 尝试从文件名中提取日期
                # 假设文件名格式为 YYYYMMDD.log
                match = re.match(r"(\d{8})\.log", filename)
                if match:
                    try:
                        file_date_str = match.group(1)
                        file_date = datetime.strptime(file_date_str, "%Y%m%d")
                        
                        # 如果文件日期早于截止日期，则删除文件
                        if file_date < cutoff_date:
                            file_path = os.path.join(log_dir, filename)
                            os.remove(file_path)
                            logger.info(f"已删除过期日志文件: {file_path}")
                    except ValueError:
                        # 如果无法解析日期，跳过该文件
                        continue
    except Exception as e:
        logger.warning(f"清理过期日志文件时出错: {e}")

def get_log_file_name():
    """按照日期格式生成日志文件名"""
    today = datetime.now().strftime("%Y%m%d")
    return os.path.join(LOG_DIR, f"{today}.log")

def setup_logger(level=LOG_LEVEL):
    """设置logger的级别和handlers"""
    global logger_handlers
    
    # 移除之前的handlers
    for handler_id in logger_handlers:
        logger.remove(handler_id)
    logger_handlers.clear()
    
    # 移除loguru默认添加的stderr处理器
    #logger.remove()
    
    # 清理过期日志文件（保留最近7天）
    clean_old_logs()
    
    # 配置 Loguru 日志器文件输出
    # 使用基于时间的轮转，例如每天轮转一次
    handler_id = logger.add(
        sink=get_log_file_name(),
        rotation="00:00",  
        retention="7 days",  # 保留7天的日志
        enqueue=True,
        encoding="utf-8",
        format="<green>{time:YYYY-MM-DD HH:mm:ss}</green> | <level>{level}</level> | {name}:{function}:{line} - {message}",
        level=level
    )
    logger_handlers.append(handler_id)
    
    # 可选：添加控制台输出器，用于调试
    # 通过设置环境变量 ENABLE_CONSOLE_LOG 来控制是否输出到控制台
    # if os.getenv("ENABLE_CONSOLE_LOG", "false").lower() == "true":
    #     handler_id = logger.add(
    #         sink=sys.stderr,
    #         format="<green>{time:YYYY-MM-DD HH:mm:ss}</green> | <level>{level}</level> | {name}:{function}:{line} - {message}",
    #         level=level
    #     )
    #     logger_handlers.append(handler_id)

def set_log_level(level):
    """动态设置日志级别"""
    # 更新环境变量
    os.environ['LOG_LEVEL'] = level
    # 重新配置logger
    setup_logger(level)
    # 同步更新标准logging模块的日志级别
    #logging.getLogger().setLevel(getattr(logging, level))

# 初始化logger
setup_logger(LOG_LEVEL)

__all__ = ["logger", "set_log_level"]