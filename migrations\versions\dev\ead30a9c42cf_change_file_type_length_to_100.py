"""change file type length to 100

Revision ID: ead30a9c42cf
Revises: 4ad8811cb3f9
Create Date: 2025-09-16 17:49:29.534397

"""
from typing import Sequence, Union

from alembic import op
import sqlalchemy as sa

import sqlmodel

# revision identifiers, used by Alembic.
revision: str = 'ead30a9c42cf'
down_revision: Union[str, None] = '4ad8811cb3f9'
branch_labels: Union[str, Sequence[str], None] = None
depends_on: Union[str, Sequence[str], None] = None


def upgrade() -> None:
    """Upgrade schema."""
    # ### commands auto generated by Alembic - please adjust! ###
    op.alter_column('user_uploaded_files', 'type',
               existing_type=sa.VARCHAR(length=50),
               type_=sqlmodel.sql.sqltypes.AutoString(length=100),
               existing_nullable=False)
    # ### end Alembic commands ###


def downgrade() -> None:
    """Downgrade schema."""
    # ### commands auto generated by Alembic - please adjust! ###
    op.alter_column('user_uploaded_files', 'type',
               existing_type=sqlmodel.sql.sqltypes.AutoString(length=100),
               type_=sa.VARCHAR(length=50),
               existing_nullable=False)
    # ### end Alembic commands ###
