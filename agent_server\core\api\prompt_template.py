import logging
from typing import Annotated, Optional, List
from agent_server.core.api.response import StandardResponse
from agent_server.core.utils.file import getFilesUrl
from fastapi import APIRouter, Depends, HTTPException, Header, Query, Path, Body

from agent_server.core.vo.user_vo import UserVO
from agent_server.core.auth.security import check_user
from agent_server.core.services.database import db_manager
from agent_server.core.services.database.crud.prompt_template import prompt_template_crud
from agent_server.core.services.database.schemas.prompt_template import (
    PromptTemplateCreate,
    PromptTemplateUpdate,
    PromptTemplateRead,
    UserPromptTemplateCreate,
    UserPromptTemplateUpdate,
)
from pydantic import BaseModel
from agent_server.core.auth.security import check_agent_permission

router = APIRouter(prefix="/prompt-template", tags=["PromptTemplate"])

logger = logging.getLogger(__name__)


@router.get("", response_model=StandardResponse[List[PromptTemplateRead]])
@check_agent_permission()
async def query_prompt_templates(
    template_type: Optional[str] = Query(
        None, description="模板类型过滤：user 或 system"
    ),
    group: Optional[str] = Query(None, description="分组过滤"),
    search: Optional[str] = Query(None, description="搜索关键词（标题或描述）"),
    skip: int = Query(0, description="跳过的记录数"),
    limit: int = Query(100, description="返回的记录数"),
    user: UserVO = Depends(check_user),
):
    """
    查询提示词模板列表

    Args:
        template_type: 模板类型过滤
        group: 分组过滤
        search: 搜索关键词
        skip: 跳过的记录数
        limit: 返回的记录数
        user: 当前用户信息

    Returns:
        提示词模板列表
    """
    try:
        async with db_manager.session() as session:
            if search:
                # 搜索模式
                templates = await prompt_template_crud.search_by_title_or_description(
                    session, search_term=search, skip=skip, limit=limit
                )
            elif template_type and group:
                # 按类型和分组过滤
                templates = await prompt_template_crud.get_by_type_and_group(
                    session,
                    template_type=template_type,
                    group=group,
                    skip=skip,
                    limit=limit,
                )
            elif template_type:
                # 按类型过滤
                templates = await prompt_template_crud.get_by_type(
                    session, template_type=template_type, skip=skip, limit=limit
                )
            elif group:
                # 按分组过滤
                templates = await prompt_template_crud.get_by_group(
                    session, group=group, skip=skip, limit=limit
                )
            else:
                # 获取所有模板
                templates = await prompt_template_crud.get_all_sorted(
                    session, skip=skip, limit=limit
                )

            return StandardResponse(data=templates, message="查询成功")
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"数据库查询错误: {str(e)}") from e


@router.get("/system", response_model=StandardResponse[List[dict]])
async def get_all_system_templates(
    agent_code: str = Query(..., description="智能体code"),
    authorization: Annotated[str | None, Header()] = None,
    user: UserVO = Depends(check_user),
):
    try:
        async with db_manager.session() as session:
            templates = await prompt_template_crud.get_all_system_sorted(
                session, agent_code=agent_code
            )
            ids = []
            for item in templates:
                if item.preview:
                    ids.append(item.preview)
            urlMap = getFilesUrl(ids, authorization=authorization, logger=logger)

        _templates = [row.model_dump() for row in templates]
        if urlMap:
            for i in _templates:
                if i["preview"]:
                    i["preview"] = urlMap[i["preview"]]

        return StandardResponse(data=_templates, message="查询成功")
    except HTTPException:
        raise
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"模板查询失败: {str(e)}") from e


@router.get("/groups", response_model=StandardResponse[List[str]])
async def get_template_groups(
    user: UserVO = Depends(check_user),
):
    """
    获取所有模板分组列表

    Args:
        user: 当前用户信息

    Returns:
        分组列表
    """
    try:
        async with db_manager.session() as session:
            groups = await prompt_template_crud.get_groups(session)
            return StandardResponse(data=groups, message="查询成功")
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"数据库查询错误: {str(e)}") from e


@router.get("/{template_id}", response_model=StandardResponse[PromptTemplateRead])
async def get_prompt_template(
    template_id: int = Path(..., description="模板ID"),
    user: UserVO = Depends(check_user),
):
    """
    根据ID获取单个提示词模板

    Args:
        template_id: 模板ID
        user: 当前用户信息

    Returns:
        提示词模板详情
    """
    try:
        async with db_manager.session() as session:
            template = await prompt_template_crud.get(session, _id=template_id)
            if not template or (
                template.create_by != user.userId and template.type == "user"
            ):
                raise HTTPException(status_code=404, detail="模板不存在")
            return StandardResponse(data=template, message="查询成功")
    except HTTPException:
        raise
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"数据库查询错误: {str(e)}") from e


@router.post("", response_model=StandardResponse[PromptTemplateRead])
async def create_prompt_template(
    template_data: PromptTemplateCreate = Body(..., description="模板创建数据"),
    user: UserVO = Depends(check_user),
):
    """
    创建新的提示词模板

    Args:
        template_data: 模板创建数据
        user: 当前用户信息

    Returns:
        创建的模板信息
    """
    try:
        async with db_manager.session() as session:
            template = await prompt_template_crud.create(
                session, obj_input=template_data
            )
            return StandardResponse(data=template, message="创建成功")
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"创建模板失败: {str(e)}") from e


@router.put("/{template_id}", response_model=StandardResponse[PromptTemplateRead])
async def update_prompt_template(
    template_id: int = Path(..., description="模板ID"),
    template_data: PromptTemplateUpdate = Body(..., description="模板更新数据"),
    user: UserVO = Depends(check_user),
):
    """
    更新提示词模板

    Args:
        template_id: 模板ID
        template_data: 模板更新数据
        user: 当前用户信息

    Returns:
        更新后的模板信息
    """
    try:
        async with db_manager.session() as session:
            # 检查模板是否存在
            existing_template = await prompt_template_crud.get(session, _id=template_id)
            if not existing_template:
                raise HTTPException(status_code=404, detail="模板不存在")

            # 更新模板
            updated_template = await prompt_template_crud.update(
                session, db_obj=existing_template, obj_in=template_data
            )
            return StandardResponse(data=updated_template, message="更新成功")
    except HTTPException:
        raise
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"更新模板失败: {str(e)}") from e


@router.delete("/{template_id}", response_model=StandardResponse[None])
async def delete_prompt_template(
    template_id: int = Path(..., description="模板ID"),
    user: UserVO = Depends(check_user),
):
    """
    删除提示词模板

    Args:
        template_id: 模板ID
        user: 当前用户信息

    Returns:
        删除结果
    """
    try:
        async with db_manager.session() as session:
            # 检查模板是否存在
            existing_template = await prompt_template_crud.get(session, _id=template_id)
            if not existing_template:
                raise HTTPException(status_code=404, detail="模板不存在")

            # 删除模板
            await prompt_template_crud.remove(session, _id=template_id)
            return StandardResponse(data=None, message="删除成功")
    except HTTPException:
        raise
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"删除模板失败: {str(e)}") from e


class ChangeOrderBody(BaseModel):
    prev_id: int | None


@router.put("/{template_id}/order", response_model=StandardResponse[None])
async def change_order(
    template_id: str = Path(..., description="模板id"),
    data: ChangeOrderBody = Body(..., description="修改排序参数"),
    user: UserVO = Depends(check_user),
):
    """
    自定义模板修改排序
    """
    # 判断模板是否当前用户所有
    try:
        async with db_manager.session() as session:
            id = int(template_id)
            prev_id = data.prev_id

            await prompt_template_crud.change_template_sort(
                session, id=id, prev_id=prev_id
            )

            return StandardResponse(data=None, message="排序修改成功")

    except HTTPException:
        raise
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"修改排序失败: {str(e)}") from e


# User-specific API endpoints for managing user prompt templates
user_router = APIRouter(prefix="/user-prompt-template", tags=["UserPromptTemplate"])


@user_router.get("", response_model=StandardResponse[List[PromptTemplateRead]])
async def get_user_prompt_templates(
    search: Optional[str] = Query(None, description="搜索关键词（标题或描述）"),
    skip: int = Query(0, description="跳过的记录数"),
    limit: int = Query(100, description="返回的记录数"),
    agent_code: str = Query(None, description="智能体"),
    user: UserVO = Depends(check_user),
):
    """
    获取当前用户的所有提示词模板

    Args:
        search: 搜索关键词
        skip: 跳过的记录数
        limit: 返回的记录数
        user: 当前用户信息

    Returns:
        用户的提示词模板列表
    """
    try:
        async with db_manager.session() as session:
            if search:
                templates = await prompt_template_crud.search_user_templates(
                    session,
                    user_id=str(user.userId),
                    search_term=search,
                    skip=skip,
                    limit=limit,
                )
            else:
                templates = await prompt_template_crud.get_user_templates(
                    session, user_id=str(user.userId), agent_code=agent_code
                )

            return StandardResponse(data=templates, message="查询成功")
    except Exception as e:
        raise HTTPException(
            status_code=500, detail=f"查询用户模板失败: {str(e)}"
        ) from e


@user_router.get("/{template_id}", response_model=StandardResponse[PromptTemplateRead])
async def get_user_prompt_template(
    template_id: int = Path(..., description="模板ID"),
    user: UserVO = Depends(check_user),
):
    """
    获取当前用户的单个提示词模板

    Args:
        template_id: 模板ID
        user: 当前用户信息

    Returns:
        提示词模板详情
    """
    try:
        async with db_manager.session() as session:
            template = await prompt_template_crud.get_user_template_by_id(
                session, template_id=template_id, user_id=str(user.userId)
            )

            if not template:
                raise HTTPException(status_code=404, detail="模板不存在或您无权限访问")

            return StandardResponse(data=template, message="查询成功")
    except HTTPException:
        raise
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"查询模板失败: {str(e)}") from e


@user_router.post("", response_model=StandardResponse[PromptTemplateRead])
async def create_user_prompt_template(
    template_data: UserPromptTemplateCreate = Body(..., description="模板创建数据"),
    user: UserVO = Depends(check_user),
):
    """
    创建新的用户提示词模板

    Args:
        template_data: 模板创建数据
        user: 当前用户信息

    Returns:
        创建的模板信息
    """
    try:
        async with db_manager.session() as session:
            templates = await prompt_template_crud.get_user_templates(
                session, user_id=str(user.userId), agent_code=template_data.agent_code
            )
            #
            sort = (
                templates[0].sort + 1
                if len(templates) > 0 and templates[0].sort is not None
                else 0
            )
            template_data.sort = sort

            template = await prompt_template_crud.create_user_template(
                session, obj_input=template_data, user_id=str(user.userId)
            )

            return StandardResponse(data=template, message="创建成功")
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"创建模板失败: {str(e)}") from e


@user_router.put("/{template_id}", response_model=StandardResponse[PromptTemplateRead])
async def update_user_prompt_template(
    template_id: int = Path(..., description="模板ID"),
    template_data: UserPromptTemplateUpdate = Body(..., description="模板更新数据"),
    user: UserVO = Depends(check_user),
):
    """
    更新用户提示词模板

    Args:
        template_id: 模板ID
        template_data: 模板更新数据
        user: 当前用户信息

    Returns:
        更新后的模板信息
    """
    try:
        async with db_manager.session() as session:
            # 检查模板是否存在且属于当前用户
            existing_template = await prompt_template_crud.get_user_template_by_id(
                session, template_id=template_id, user_id=str(user.userId)
            )

            if not existing_template:
                raise HTTPException(status_code=404, detail="模板不存在或您无权限修改")

            # 更新模板
            updated_template = await prompt_template_crud.update_user_template(
                session,
                db_obj=existing_template,
                obj_in=template_data,
                user_id=str(user.userId),
            )

            return StandardResponse(data=updated_template, message="更新成功")
    except HTTPException:
        raise
    except ValueError as e:
        raise HTTPException(status_code=403, detail=str(e)) from e
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"更新模板失败: {str(e)}") from e


@user_router.delete("/{template_id}", response_model=StandardResponse[None])
async def delete_user_prompt_template(
    template_id: int = Path(..., description="模板ID"),
    user: UserVO = Depends(check_user),
):
    """
    删除用户提示词模板

    Args:
        template_id: 模板ID
        user: 当前用户信息

    Returns:
        删除结果
    """
    try:
        async with db_manager.session() as session:
            # 尝试删除模板
            success = await prompt_template_crud.delete_user_template(
                session, template_id=template_id, user_id=str(user.userId)
            )

            if not success:
                raise HTTPException(status_code=404, detail="模板不存在或您无权限删除")

            return StandardResponse(data=None, message="删除成功")
    except HTTPException:
        raise
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"删除模板失败: {str(e)}") from e


@user_router.put("/{template_id}/order", response_model=StandardResponse[None])
async def change_user_template_order(
    template_id: str = Path(..., description="模板id"),
    data: ChangeOrderBody = Body(..., description="修改排序参数"),
    user: UserVO = Depends(check_user),
):
    """
    自定义模板修改排序
    """
    # 判断模板是否当前用户所有
    try:
        async with db_manager.session() as session:
            id = int(template_id)
            prev_id = data.prev_id
            user_id = str(user.userId)

            await prompt_template_crud.change_user_template_sort(
                session, id=id, prev_id=prev_id, user_id=user_id
            )
            return StandardResponse(data=None, message="排序修改成功")

    except HTTPException:
        raise
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"修改排序失败: {str(e)}") from e


# Add the user router to the main router
router.include_router(user_router)
