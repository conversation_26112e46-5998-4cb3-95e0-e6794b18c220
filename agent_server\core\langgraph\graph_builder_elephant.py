from agent_server.core.langgraph.graph_builder import BaseGraphBuilder, AgentState
from typing import List, Dict, Any, Optional
from langchain_core.messages import AIMessage
from langchain_core.tools import BaseTool
from langgraph.graph import StateGraph, START
from langgraph.prebuilt import ToolNode
from agent_server.core.config.app_config import ModelConfig
from agent_server.core.config.app_logger import logger
from langgraph.types import Command, interrupt
import json
from langchain_core.tools import tool
from agent_server.utils.common_utils import CommonUtils
safe_json_dumps = CommonUtils.safe_json_dumps

# 示例代码，仅做参考
# <AUTHOR> xiangjh

class EleAgentState(AgentState):
    """Elephant Agent 专用状态定义

    继承基础 AgentState，并添加企业查询相关的特有属性

    Attributes:
        company_list: 公司列表信息，用于存储查询到的多个公司结果
        selected_company_code: 用户选择的公司代码
    """
    company_list: List[dict]
    selected_company_code: Optional[str]

@tool
def query_company_info(company_name: str = None):
    """
    查询指定公司名称查询公司信息，如果查询到多笔，则返回列表，否则返回空列表。

    Parameters:
        company_name (str, optional): 公司名称

    Returns:
        str: JSON格式的公司收入数据列表
    """
    all_data = [
        {"companycode": "000001", "companyname": "苹果公司"},
        {"companycode": "000002", "companyname": "微软公司"},
        {"companycode": "000003", "companyname": "谷歌公司"},
        {"companycode": "000004", "companyname": "亚马逊"},
        {"companycode": "000005", "companyname": "特斯拉"},
        {"companycode": "000006", "companyname": "英伟达"},
        {"companycode": "000007", "companyname": "阿里巴巴"},
        {"companycode": "000008", "companyname": "腾讯"},
        {"companycode": "000009", "companyname": "深圳万科"},
        {"companycode": "000010", "companyname": "成都万科"},
    ]

    # 如果提供了公司名称，则筛选匹配的公司
    if company_name:
        filtered_data = [item for item in all_data if company_name in item["companyname"]]
        if not filtered_data:
            # 如果没有匹配的公司，返回一条默认数据
            filtered_data = [{"companyname": company_name, "companytype": "未知", "companyaddress": "未知"}]
        result = filtered_data
    else:
        # 如果没有提供公司名称，返回空数据
        result = []
    logger.info(f"调用tools查询公司信息: {result}")
    return result


@tool
def query_companyincome(company_code: str) -> str:
    """查询指定companycode的公司名称的营业收入，返回一个包含公司名称和收入的列表"""
    """
    查询指定companycode的公司名称的营业收入，返回一个包含公司名称和收入的列表

    Parameters:
        company_name (str, optional): 公司名称，如果不提供则返回一些示例数据

    Returns:
        str: JSON格式的公司收入数据列表
    """
    sample_data = [
        {"companycode": "000001","companyname": "苹果公司", "income": 394320000000},
        {"companycode": "000002","companyname": "微软公司", "income": 211910000000},
        {"companycode": "000003","companyname": "谷歌公司", "income": 282830000000},
        {"companycode": "000004","companyname": "亚马逊", "income": 574780000000},
        {"companycode": "000005","companyname": "特斯拉", "income": 96773000000},
        {"companycode": "000006","companyname": "英伟达", "income": 60922000000},
        {"companycode": "000007","companyname": "阿里巴巴", "income": 132070000000},
        {"companycode": "000008","companyname": "腾讯", "income": 869120000000},
        {"companycode": "000009","companyname": "深圳万科", "income": 68889},
        {"companycode": "000010","companyname": "成都万科", "income": 45000},
    ]

    # 如果提供了公司名称，则筛选匹配的公司
    if company_code:
        filtered_data = [item for item in sample_data if company_code in item["companycode"]]
        if not filtered_data:
            # 如果没有匹配的公司，返回一条默认数据
            filtered_data = [{"companyname": company_code, "income": 0}]
        result = filtered_data
    else:
        # 如果没有提供公司名称，返回空数据
        result = []

    return safe_json_dumps({"success": True, "data": result})

class ElephantGraphBuilder(BaseGraphBuilder):
    """自定义LangGraph构建器"""

    def should_continue(self, state: EleAgentState) -> str:
        """决定是否继续执行工具"""
        messages = state["messages"]
        last_message = messages[-1]
        logger.info(f"should_continue 检查最后消息: {last_message}")

        if hasattr(last_message, 'tool_calls') and last_message.tool_calls:
            logger.info(f"检测到工具调用，路由到 tools 节点: {last_message.tool_calls}")
            return "tools"
        else:
            logger.info("未检测到工具调用，路由到 check_results 节点")
            return "check_results"

    def should_continue2(self, state: EleAgentState) -> str:
        """决定是否继续执行工具"""
        messages = state["messages"]
        last_message = messages[-1]
        if hasattr(last_message, 'tool_calls') and last_message.tool_calls:
            return "tools2"
        return "end"

    def route_after_check_results(self, state: EleAgentState) -> str:
        """检查结果后的路由决策"""
        company_list = state.get("company_list", [])
        selected_company_code = state.get("selected_company_code")

        logger.info(f"路由决策 - company_list: {company_list}, selected_company_code: {selected_company_code}")

        # 如果有选中的公司（单个结果或用户已选择），继续到查询收入
        if selected_company_code or len(company_list) == 1:
            logger.info("路由到 query_income 节点")
            return "query_income"
        elif len(company_list) > 1:
            # 多个结果需要中断等待用户选择
            logger.info("多个结果，路由到中断节点")
            return "handle_multiple_companies"
        else:
            # 没有结果，也路由到 query_income（可能需要处理错误）
            logger.warning("没有公司结果，路由到 query_income")
            return "query_income"
    def handle_multiple_companies(self, state: EleAgentState) -> Command:
        """处理多个公司结果的中断节点"""
        company_list = state.get("company_list", [])
        logger.info(f"handle_multiple_companies 接收到的 company_list: {company_list}")

        if not company_list:
            logger.error("company_list 为空，无法处理多公司选择")
            return Command(goto="query_income")

        options = [f"{i+1}. {company['companyname']}({company['companycode']})"
                  for i, company in enumerate(company_list)]

        selection = interrupt({
            "message": "检测到多个匹配结果，请确认一个：",
            "options": options
        })

        # 恢复后处理用户选择
        logger.info(f"用户选择: {selection}")
        user_input = selection.get("data", "")

        # 验证用户输入是否有效
        if self.is_valid_selection(user_input, len(company_list)):
            # 输入有效，处理选择并继续到下一节点
            self.handle_user_selection(state, user_input)
            return Command(goto="query_income")
        else:
            # 输入无效，继续中断让用户重新选择
            logger.warning(f"用户输入无效: {user_input}，继续中断等待重新选择")
            # 添加错误提示消息
            error_msg = f"选择无效，请输入1到{len(company_list)}之间的数字。"
            state["messages"].append(AIMessage(content=error_msg))

            # 重新触发中断
            return self.handle_multiple_companies(state)
    def is_valid_selection(self, user_input: str, max_options: int) -> bool:
        """验证用户选择是否有效"""
        try:
            selection = int(user_input.strip())
            return 1 <= selection <= max_options
        except ValueError:
            return False

    def check_tool_results(self, state: EleAgentState) -> Dict[str, Any]:
        """检查工具执行结果"""
        messages = state["messages"]
        logger.info(f"检查工具执行结果，消息数量: {len(messages)}")

        if not messages:
            logger.warning("没有消息历史")
            return {}

        # 打印所有消息类型，帮助调试
        for i, msg in enumerate(messages):
            logger.debug(f"消息 {i}: 类型={type(msg).__name__}, 内容={str(msg)}")

        # 查找 ToolMessage（工具执行结果）
        tool_results = None
        for message in reversed(messages):
            # 检查是否是 ToolMessage
            if hasattr(message, 'tool_call_id') or type(message).__name__ == 'ToolMessage':
                logger.info(f"找到 ToolMessage: {message}")
                try:
                    content = message.content
                    if isinstance(content, str):
                        # 尝试解析 JSON
                        if content.strip().startswith('[') or content.strip().startswith('{'):
                            tool_results = json.loads(content)
                        else:
                            tool_results = content
                    else:
                        tool_results = content
                    logger.info(f"解析到工具结果: {tool_results}")
                    break
                except Exception as e:
                    logger.error(f"解析 ToolMessage 失败: {str(e)}")
                    continue

        # 如果没找到 ToolMessage，可能工具结果在其他地方
        if tool_results is None:
            logger.warning("未找到 ToolMessage，检查其他可能的结果位置")
            # 检查 state 中是否有工具结果
            if 'tool_result' in state:
                tool_results = state['tool_result']
                logger.info(f"从 state['tool_result'] 获取结果: {tool_results}")

        # 解析公司列表
        company_list = []
        if tool_results:
            try:
                if isinstance(tool_results, list):
                    company_list = tool_results
                elif isinstance(tool_results, dict):
                    if 'data' in tool_results:
                        company_list = tool_results['data']
                    elif 'results' in tool_results:
                        company_list = tool_results['results']
                    else:
                        company_list = [tool_results]
                elif isinstance(tool_results, str):
                    # 尝试解析字符串
                    parsed = json.loads(tool_results)
                    if isinstance(parsed, list):
                        company_list = parsed
                    elif isinstance(parsed, dict) and 'data' in parsed:
                        company_list = parsed['data']
                    else:
                        company_list = [parsed]
            except Exception as e:
                logger.error(f"解析工具结果失败: {str(e)}")
                # 如果解析失败，使用默认测试数据
                company_list = []

        logger.info(f"最终解析的公司列表: {company_list}")

        if len(company_list) > 1:
            logger.info(f"检测到多个匹配结果: {len(company_list)} 个公司")
            return {"company_list": company_list}
        elif len(company_list) == 1:
            sel = company_list[0]
            logger.info(f"单个结果，自动选择: {sel.get('companyname', 'Unknown')}")
            return {
                "company_list": company_list,
                "selected_company_code": sel.get("companycode")
            }
        else:
            logger.warning("未找到任何公司结果")
            return {"company_list": []}


    def handle_user_selection(self, state: EleAgentState, user_input: str) -> Dict[str, Any]:
        """处理用户选择（调用前已验证输入有效性）"""
        company_list = state.get("company_list", [])
        selection = int(user_input.strip())
        selected_company = company_list[selection - 1]

        ai_message = f"您已选择: {selected_company['companyname']}({selected_company['companycode']})"
        logger.info(ai_message)

        # 更新状态：设置选中的公司和公司代码
        state["company_list"] = [selected_company]
        state["selected_company_code"] = selected_company.get("companycode")
        state["messages"].append(AIMessage(content=ai_message))

        return state

    def build_langgraph_agent(self, agent_id: str, tools: List[BaseTool], model_config: ModelConfig):
        """实现自定义流程图"""
        # 定义图
        workflow = StateGraph(EleAgentState)

        tools = [query_company_info]
        tools2 = [query_companyincome]
        # 添加节点到图中
        # 一、根据用户输入，查询企业信息
        workflow.add_node("query_company", self.create_agent_node(
                agent_id,
                "query_company",
                tools,
                model_config,
                run_config={"run_name": "aaaaaa", "tags": ["company_query"]}
        ))
        workflow.add_node("tools", self.create_tool_node(tools))
        workflow.add_node("check_results", self.check_tool_results)  # 添加检查结果节点
        workflow.add_node("handle_multiple_companies", self.handle_multiple_companies)  # 添加中断处理节点

        # 二、根据企业信息，查询企业收入
        workflow.add_node("query_income",
            self.create_agent_node(agent_id, "query_income", tools2, model_config)
        )
        workflow.add_node("tools2", self.create_tool_node(tools2))




        # 设置入口点
        workflow.add_edge(START, "query_company")

        # 添加条件边: query_company节点之后的决策
        workflow.add_conditional_edges(
            "query_company",
            self.should_continue,
            {
                "tools": "tools",
                "check_results": "check_results",
            }
        )

        # 添加边：工具 -> 检查结果
        workflow.add_edge("tools", "check_results")

        # 添加条件边：检查结果后的决策（中断或继续）
        workflow.add_conditional_edges(
            "check_results",
            self.route_after_check_results,
            {
                "query_income": "query_income",
                "handle_multiple_companies": "handle_multiple_companies",  # 多个结果时中断
            }
        )

        # 中断处理节点完成后直接到查询收入（通过 Command 控制）

        # 添加边：查询收入 -> 工具2
        workflow.add_conditional_edges(
            "query_income",
            self.should_continue2,
            {
                "tools2": "tools2",
                "end": "__end__",
            }
        )
        workflow.add_edge("tools2", "query_income")


        # 只返回工作流定义，编译步骤已移至 LangGraphBaseSender._generate_content
        return workflow