"""add_rag_server_field_to_user_uploaded_files

Revision ID: eac65d3a9464
Revises: da98e4783e91
Create Date: 2025-09-11 12:06:06.594758

"""
from typing import Sequence, Union

from alembic import op
import sqlalchemy as sa

import sqlmodel

# revision identifiers, used by Alembic.
revision: str = 'eac65d3a9464'
down_revision: Union[str, None] = 'da98e4783e91'
branch_labels: Union[str, Sequence[str], None] = None
depends_on: Union[str, Sequence[str], None] = None


def upgrade() -> None:
    """Upgrade schema."""
    # Add rag_server column to user_uploaded_files table
    op.add_column('user_uploaded_files',
                  sa.Column('rag_server',
                           sqlmodel.sql.sqltypes.AutoString(length=50),
                           nullable=False,
                           server_default='ragflow'))


def downgrade() -> None:
    """Downgrade schema."""
    # Remove rag_server column from user_uploaded_files table
    op.drop_column('user_uploaded_files', 'rag_server')
