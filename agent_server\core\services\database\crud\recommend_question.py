from typing import List
from sqlalchemy.ext.asyncio import AsyncSession
from sqlmodel import select
from datetime import datetime, timezone

from agent_server.core.services.database.crud.base import CRUDBase
from agent_server.core.services.database.schemas.recommend_question import (
    RecommendQuestionTable,
    RecommendQuestionCreate,
    RecommendQuestionUpdate,
)


class CRUDRecommendQuestion(CRUDBase[RecommendQuestionTable, RecommendQuestionCreate, RecommendQuestionUpdate]):
    """推荐问题CRUD操作实现"""

    async def get_by_condition(
        self, 
        db: AsyncSession, 
        *, 
        agent_code: str, 
        question: str, 
        skip: int = 0, 
        limit: int = 100
    ) -> List[RecommendQuestionTable]:
        """根据智能体和问题获取推荐问题列表，按order字段升序排序"""
        queryData = select(RecommendQuestionTable)
        if agent_code :
            queryData = queryData.where(RecommendQuestionTable.agent_code == agent_code)
        
        if question :
            queryData = queryData.where(RecommendQuestionTable.agent_code.like(f"%{question}%"))

        queryData = queryData.order_by(RecommendQuestionTable.order.asc()).offset(skip).limit(limit)
        query = (queryData)
        result = await db.execute(query)
        return result.scalars().all()

    async def create(
        self, 
        db: AsyncSession, 
        *, 
        db_obj: RecommendQuestionTable, 
        obj_in: RecommendQuestionCreate
    ) -> RecommendQuestionTable:
        """新增推荐问题模板，自动设置create_time时间"""
        obj_data = obj_in.model_dump(exclude_unset=True)
        obj_data["create_time"] = datetime.now(timezone.utc)
        
        for field, value in obj_data.items():
            setattr(db_obj, field, value)
        
        db.add(db_obj)
        await db.commit()
        await db.refresh(db_obj)
        return db_obj


    async def update(
        self, 
        db: AsyncSession, 
        *, 
        db_obj: RecommendQuestionTable, 
        obj_in: RecommendQuestionUpdate
    ) -> RecommendQuestionTable:
        """更新推荐问题模板，自动设置updated_time时间"""
        obj_data = obj_in.model_dump(exclude_unset=True)
        obj_data["updated_time"] = datetime.now(timezone.utc)
        
        for field, value in obj_data.items():
            setattr(db_obj, field, value)
        
        db.add(db_obj)
        await db.commit()
        await db.refresh(db_obj)
        return db_obj
   


# 创建CRUD实例
recommend_question_crud = CRUDRecommendQuestion(RecommendQuestionTable)
