
from fastapi import Header, HTTPException, Depends
from typing import Annotated
import requests
from agent_server.core.config.app_config import config
from agent_server.core.vo.user_vo import UserVO
from agent_server.utils.redis_util import redis_client
from agent_server.core.auth.adapter import AuthAdapter
from agent_server.core.auth.adapter_factory import get_auth_adapter
from agent_server.core.config.app_logger import logger
from functools import wraps

# 依赖注入式的用户认证装饰器
# 系统鉴权工具类（通过调用 pre_authorize 和 checkuser 分别实现鉴权和登录验证）
# <AUTHOR> xiangjh

async def get_current_user(authorization: Annotated[str, Header()] = None):
    try:
        if not authorization:
            raise HTTPException(status_code=401, detail="用户未认证")
        if not authorization.lower().startswith("bearer "):
            raise HTTPException(status_code=401, detail="无效的认证头")

        token = authorization[7:].strip()
        print(f"获取到用户令牌: {token}")

        check_auth_url = config.auth.auth_url 
        if not check_auth_url:
            raise HTTPException(status_code=500, detail="认证服务地址未配置")

        response = requests.get(
            check_auth_url,
            headers={
                "Authorization": authorization,
                "Content-Type": "application/json"
            },
            timeout=5
        )

        if response.status_code != 200:
            logger.error(f"认证服务返回非200状态码: {response.status_code}")
            raise HTTPException(status_code=401, detail="认证失败")

        try:
            json_data = response.json()
        except ValueError as ve:
            logger.error(f"JSON解析失败: {ve}")
            raise HTTPException(status_code=500, detail="认证服务返回格式错误")


        # 解析用户数据
        adapter: AuthAdapter = get_auth_adapter()
        curr_user = adapter.parse_user_data(json_data)

        # Step 3: 写入 Redis 缓存
        user_cache_data = {
            "userId": curr_user.userId,
            "username": curr_user.username,
            "nickName": curr_user.nickName,
            "permissions": list(curr_user.permissions)
        }
        redis_client.set_user(token, user_cache_data)

        return curr_user

    except requests.exceptions.RequestException as re:
        logger.error(f"请求认证服务失败: {re}")
        raise HTTPException(status_code=503, detail="认证服务不可用")

    except Exception as e:
        if not isinstance(e, HTTPException):
            logger.error(f"获取当前用户时发生异常: {e}", exc_info=True)
            raise HTTPException(status_code=500, detail="认证失败!")

# 依赖注入式的权限校验装饰器 
# 接收一个权限集合参数 permissions，表示调用接口所需的权限，若权限不足，则抛出 403 异常；否则返回用户对象

def pre_authorize(permissions: set[str]):
    async def authorize_dependency(user: UserVO = Depends(get_current_user)):
        if not permissions.issubset(user.permissions):
            raise HTTPException(status_code=403, detail="Permission denied")
        return user
    # 返回函数本身，FastAPI 会自动包装成 Depends
    return authorize_dependency

async def check_user_dependency(user: UserVO = Depends(get_current_user)):
    if user is None:
        raise HTTPException(status_code=401, detail="用户未认证")
    return user

# 用户认证装饰器，用于检查用户是否已登录，若未登录则抛出 401 异常；否则返回用户对象

async def check_user(user: UserVO = Depends(check_user_dependency)):
    return user

def filter_by_agent_permission():
    """权限过滤器装饰器"""
    def decorator(func):
        async def wrapper(*args, **kwargs):
            # 获取当前用户
            user = kwargs.get('user') or await get_current_user(kwargs.get('authorization'))
            
            # 获取原始结果
            result = await func(*args, **kwargs)

            # 如果用户权限为空，则直接返回原始结果
            if not user.permissions:
                return result
            
            if isinstance(result, list):
                allowed_agents = set(user.permissions)
                return [item for item in result if str(getattr(item, 'agent_code', None)) in allowed_agents]
            
            return result
        return wrapper
    return decorator

def check_agent_permission():
    """检查用户是否有访问指定agent_code的权限"""
    def decorator(func):
        @wraps(func)
        async def wrapper(*args, **kwargs):
            # 获取当前用户
            user = kwargs.get('user') or await get_current_user(kwargs.get('authorization'))
            
            # 获取agent_code参数
            agent_code = kwargs.get('agent_code')
            
            # 如果指定了agent_code但用户没有权限
            if agent_code and str(agent_code) not in user.permissions:
                raise HTTPException(
                    status_code=403, 
                    detail=f"无权访问智能体 {agent_code}"
                )
            
            return await func(*args, **kwargs)
        return wrapper
    return decorator