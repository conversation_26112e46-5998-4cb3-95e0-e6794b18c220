from typing import Optional, List
from fastapi import APIRouter, Depends, HTTPException, Query, Path, Body

from agent_server.core.vo.user_vo import UserVO
from agent_server.core.auth.security import check_user
from agent_server.core.services.database import db_manager
from agent_server.core.services.database.crud.recommend_question import recommend_question_crud
from agent_server.core.services.database.schemas.recommend_question import (
    RecommendQuestionCreate,
    RecommendQuestionUpdate,
    RecommendQuestionRead,
)

router = APIRouter(prefix="/recommend-question", tags=["RecommendQuestion"])


@router.get("", response_model=List[RecommendQuestionRead])
async def query_recommend_questions(
    agent_code: Optional[str] = Query(None, description="智能体code"),
    question: Optional[str] = Query(None, description="问题"),
    skip: int = Query(0, description="跳过的记录数"),
    limit: int = Query(100, description="返回的记录数"),
    user: UserVO = Depends(check_user),
):
    """
    查询推荐问题列表
    
    Args:
        agent_code: 智能体code
        question: 问题
        skip: 跳过的记录数
        limit: 返回的记录数
        user: 当前用户信息
    
    Returns:
        推荐问题列表
    """
    try:
        async with db_manager.session() as session:
            questions = await recommend_question_crud.get_by_condition(
                    session,
                    agent_code = agent_code,
                    question = question, 
                    skip=skip, 
                    limit=limit
                )
            
            return {
                "code": 200,
                "data": questions,
                "message": "查询成功",
            }
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"数据库查询错误: {str(e)}") from e


@router.get("/{recommend_id}", response_model=RecommendQuestionRead)
async def get_recommend_question(
    recommend_id: int = Path(..., description="问题ID"),
    user: UserVO = Depends(check_user),
):
    """
    根据ID获取单个推荐问题
    
    Args:
        template_id: 问题ID
        user: 当前用户信息
    
    Returns:
        推荐问题详情
    """
    try:
        async with db_manager.session() as session:
            template = await recommend_question_crud.get(session, _id=recommend_id)
            if not template:
                raise HTTPException(status_code=404, detail="推荐问题不存在")
            return template
    except HTTPException:
        raise
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"数据库查询错误: {str(e)}") from e


@router.post("", response_model=RecommendQuestionRead)
async def create_recommend_question(
    recommend_question: RecommendQuestionCreate = Body(..., description="问题创建数据"),
    user: UserVO = Depends(check_user),
):
    """
    创建新的推荐问题
    
    Args:
        recommend_question: 问题创建数据
        user: 当前用户信息
    
    Returns:
        创建的问题信息
    """
    try:
        async with db_manager.session() as session:
            recommend_question.create_by = user.userId
            question = await recommend_question_crud.create(session, obj_input=recommend_question)
            return question
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"创建推荐问题失败: {str(e)}") from e


@router.put("/{recommend_id}", response_model=RecommendQuestionRead)
async def update_recommend_question(
    recommend_id: int = Path(..., description="推荐问题ID"),
    recommend_question: RecommendQuestionUpdate = Body(..., description="推荐问题更新数据"),
    user: UserVO = Depends(check_user),
):
    """
    更新推荐问题
    
    Args:
        recommend_id: 推荐问题ID
        recommend_question: 推荐问题更新数据
        user: 当前用户信息
    
    Returns:
        更新后的推荐问题信息
    """
    try:
        async with db_manager.session() as session:
            # 检查推荐问题是否存在
            existing_question = await recommend_question_crud.get(session, _id=recommend_id)
            if not existing_question:
                raise HTTPException(status_code=404, detail="推荐问题不存在")
            
            # 更新推荐问题
            updated_template = await recommend_question_crud.update(
                session, db_obj=existing_question, obj_in=recommend_question
            )
            return updated_template
    except HTTPException:
        raise
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"更新推荐问题失败: {str(e)}") from e


@router.delete("/{recommend_id}")
async def delete_recommend_question(
    recommend_id: int = Path(..., description="推荐问题ID"),
    user: UserVO = Depends(check_user),
):
    """
    删除推荐问题
    
    Args:
        recommend_id: 推荐问题ID
        user: 当前用户信息
    
    Returns:
        删除结果
    """
    try:
        async with db_manager.session() as session:
            # 检查推荐问题是否存在
            existing_template = await recommend_question_crud.get(session, _id=recommend_id)
            if not existing_template:
                raise HTTPException(status_code=404, detail="推荐问题不存在")
            
            # 删除推荐问题
            await recommend_question_crud.remove(session, _id=recommend_id)
            return {
                "code": 200,
                "message": "删除成功",
            }
    except HTTPException:
        raise
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"删除推荐问题失败: {str(e)}") from e
