import json
import threading
import traceback
import asyncio
from agent_server.core.services.database.base import DatabaseManager
from fastapi import HTTP<PERSON>xception
from typing import Any, Dict
from uuid import uuid4
from datetime import datetime, timezone

from agent_server.utils.redis_util import redis_client
from agent_server.core.message.transmitter import Transmitter
from agent_server.core.services.database import db_manager
from agent_server.core.services.database.crud.conversation import conversation_curd
from agent_server.core.services.database.schemas.conversation import (
    ConversationCreate,
    ConversationTable,
)
from agent_server.core.message.types import MessageType, MessagePackage
from agent_server.core.services.database.schemas.message import MessageCreate
from agent_server.core.services.database.crud.message import message_curd
from agent_server.core.config.app_config import config


def push_message_chunk(task_id: str, message: str):
    redis_client.client.lpush(task_id, message)


def getPreservedKey(key: str):
    return f"{key}_preserved"


async def message_generate(task_id: str, message_generator: Any):
    try:
        async for msg in message_generator:
            # 收到 cancel 信号中断生成
            head = redis_client.client.lindex(task_id, 0)
            if head and head.startswith("[CANCEL]:"):
                return
            push_message_chunk(task_id=task_id, message=msg)
    except Exception as e:
        print(f"Error: {e}")
        push_message_chunk(task_id=task_id, message="[ERROR]:  " + str(e) + "\n")
    finally:
        push_message_chunk(task_id=task_id, message="[DONE]")


async def run_generate_message_task(
    task_id: str,
    conversation_id: str,
    agent_code: str,
    message_generator_fn: Any,
):
    """Run task in thread with synchronous database operations"""
    push_message_chunk(task_id=task_id, message="[START]")
    # 消息任务有效期24小时
    redis_client.client.expire(task_id, config.message.task_expired)

    # Get the database URL in the main thread to avoid threading issues
    # main_thread_db_url = config.database.url

    async def task():
        # Create synchronous database connection for this thread
        # Use the URL from the main thread to avoid threading issues
        # db_url = main_thread_db_url

        # if not db_url:
        #     error_message = "Error: Database URL is not configured"
        #     print(f"Error: {error_message}")
        #     push_message_chunk(task_id=task_id, message=f"[ERROR]: {error_message}")
        #     return

        # # Convert async URL to sync URL
        # if "postgresql+asyncpg://" in db_url:
        #     sync_url = db_url.replace("postgresql+asyncpg://", "postgresql://")
        # else:
        #     sync_url = db_url

        # try:
        #     sync_engine = create_engine(sync_url, pool_pre_ping=True, pool_recycle=3600)
        # except Exception as e:
        #     error_message = f"Error creating database engine: {e}"
        #     print(error_message)
        #     push_message_chunk(task_id=task_id, message=f"[ERROR]: {error_message}")
        #     return
        # SessionLocal = sessionmaker(bind=sync_engine)
        # session = SessionLocal()


        try:
            session = DatabaseManager.createScopedSession()
            transmitter = Transmitter(
                conversation_id=conversation_id,
                message_id=uuid4().hex,
                agent_code=agent_code,
                use_sync_db=True,
                sync_session=session,
            )

            await message_generate(
                task_id,
                message_generator_fn(transmitter),
            )

        except Exception as e:
            print(f"Task error: {e}")
            push_message_chunk(task_id=task_id, message=f"[ERROR]: {e}")

        finally:
            if session:
                session.remove()

    def run_task():
        asyncio.run(task())

    # Run in a separate thread
    thread = threading.Thread(target=run_task)
    thread.daemon = True  # Thread will die when main program exits
    thread.start()


def subscribe_message(task_id):
    # 接收消息 100s 超时
    result = redis_client.client.brpop(task_id, config.message.waiting_timeout)

    if result and result[1]:
        preserved_data_key = getPreservedKey(task_id)
        existed_preserved = redis_client.client.exists(preserved_data_key)
        redis_client.client.lpush(preserved_data_key, result[1])
        if not existed_preserved:
            # 消息任务有效期24小时
            redis_client.client.expire(preserved_data_key, config.message.task_expired)

        return result[1]
    else:
        return None


async def content_stream_generator(task_id: str, conversation_id: str):
    # TODO: 优化异步问题，等待任务更新到 redis
    await asyncio.sleep(0.5)
    try:
        if redis_client.client.exists(task_id):
            preserved_data_key = getPreservedKey(task_id)
            all_created_messages = redis_client.client.lrange(preserved_data_key, 0, -1)
            if all_created_messages:
                all_created_messages.reverse()
                for message in all_created_messages:
                    if message == "[START]":
                        continue
                    if message == "[DONE]":
                        return
                    if message.startswith("[CANCEL]:"):
                        return
                    yield message

            while True:
                message = await asyncio.to_thread(subscribe_message, task_id)
                if not message:
                    return
                if message == "[START]":
                    continue
                if message == "[DONE]":
                    return
                if message.startswith("[CANCEL]:"):
                    return
                if message.startswith("[ERROR]:"):
                    raise Exception(message[7:])
                else:
                    yield message
        else:
            # 任务异常，移除任务，提示重试
            if conversation_id:
                async with db_manager.session() as session:
                    conversation_list = await conversation_curd.get_by_conversation_id(
                        session, _id=conversation_id
                    )
                    if conversation_list:
                        await conversation_curd.update(
                            db=session,
                            db_obj=conversation_list[0],
                            obj_input={"task_id": None},
                        )
            # raise HTTPException(status_code=404, detail="消息任务异常")
            yield "[DONE]"
    except HTTPException:
        yield ""
    except Exception as e:
        traceback.print_exc()

        # 返回给前端的错误信息包含：
        error_info = {
            "error_type": type(e).__name__,
            "error_message": str(e),
            "stack_trace": traceback.format_exc(),
        }
        yield json.dumps({
            "code": 500,
            "message": f"{type(e).__name__} - {str(e)}",
            "error": error_info,
        })


async def create_update_conversation(
    message: str,
    conversation: ConversationTable | None,
    agent_code: str,
    user_id: int | str,
):
    task_id = f"message-task:{uuid4().hex}"
    # 新建会话，更新task_id
    conversation_id = None
    if conversation:
        conversation_id = conversation.id

    async with db_manager.session() as session:
        if not conversation:
            title = message[:20]
            new_conversation = ConversationCreate(
                user_id=user_id,
                title=title,
                created_at=datetime.now(timezone.utc),
                updated_at=datetime.now(timezone.utc),
                current_agent_code=agent_code,
                task_id=task_id,
            )
            new_conversation = await conversation_curd.create(
                db=session,
                obj_input=new_conversation,
            )
            conversation_id = new_conversation.id
        else:
            if conversation:
                await conversation_curd.update(
                    db=session,
                    db_obj=conversation,
                    obj_input={"task_id": task_id},
                )

        return conversation_id, task_id


async def save_user_message(*, message: str, agent_code: str, conversation_id: str, data: Dict[str, Any], hidden = False):
    # 保存用户消息
    message_pkg = MessagePackage(
        package_id=0,
        package_type=0,
        status=1,
        data=message,
    )
    async with db_manager.session() as session:
        new_message = MessageCreate(
            agent_code=agent_code,
            conversation_id=conversation_id,
            message_type=MessageType.HUMAN,
            content=[message_pkg.model_dump()],
            data_object=data,
            hidden=hidden,
        )
        await message_curd.create(db=session, obj_input=new_message)
