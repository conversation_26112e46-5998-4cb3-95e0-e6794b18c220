"""
Test the refactored delete_files API endpoint to ensure it properly uses RAGServiceBase.delete_files
"""
import unittest
from unittest.mock import AsyncMock, patch
from fastapi import HTTPException

from agent_server.core.rag.rag_service import FileDeleteResult
from agent_server.core.vo.user_vo import UserVO


class TestDeleteFilesAPIRefactor(unittest.IsolatedAsyncioTestCase):
    """Test the refactored delete_files API endpoint"""
    
    def setUp(self):
        """Set up test environment"""
        self.test_user = UserVO(userId=123, username="testuser")
        self.test_file_ids = ["file1", "file2"]
    
    @patch('core.api.rag.get_rag_service')
    @patch('core.api.rag.check_user')
    async def test_delete_files_uses_base_class_method(self, mock_check_user, mock_get_rag_service):
        """Test that the API endpoint uses RAGServiceBase.delete_files method"""
        # Mock user authentication
        mock_check_user.return_value = self.test_user
        
        # Mock RAG service and its delete_files method
        mock_rag_service = AsyncMock()
        mock_get_rag_service.return_value = mock_rag_service
        
        # Mock the delete_files method to return a successful result
        expected_result = FileDeleteResult(
            success=True,
            deleted_files=self.test_file_ids,
            failed_files=[],
            total_deleted=2,
            total_failed=0
        )
        mock_rag_service.delete_files.return_value = expected_result
        
        # Import the delete_files function from the API module
        from agent_server.core.api.rag import delete_files
        from agent_server.core.api.rag import FileBatchDeleteRequest
        
        # Create request object
        request = FileBatchDeleteRequest(file_ids=self.test_file_ids)
        
        # Call the API function
        result = await delete_files(request, self.test_user)
        
        # Verify that the RAG service's delete_files method was called with correct parameters
        mock_rag_service.delete_files.assert_called_once_with(
            ids=self.test_file_ids,
            user_id=str(self.test_user.userId)
        )
        
        # Verify the response structure
        self.assertEqual(result.code, 200)
        self.assertEqual(result.data.deleted_files, self.test_file_ids)
        self.assertEqual(result.data.failed_files, [])
        self.assertEqual(result.data.total_deleted, 2)
        self.assertEqual(result.data.total_failed, 0)
        self.assertIn("2 succeeded, 0 failed", result.message)
    
    @patch('core.api.rag.get_rag_service')
    @patch('core.api.rag.check_user')
    async def test_delete_files_handles_partial_failure(self, mock_check_user, mock_get_rag_service):
        """Test that the API endpoint handles partial failures correctly"""
        # Mock user authentication
        mock_check_user.return_value = self.test_user
        
        # Mock RAG service
        mock_rag_service = AsyncMock()
        mock_get_rag_service.return_value = mock_rag_service
        
        # Mock partial failure result
        expected_result = FileDeleteResult(
            success=False,
            deleted_files=["file1"],
            failed_files=[{"file_id": "file2", "error": "Access denied"}],
            total_deleted=1,
            total_failed=1
        )
        mock_rag_service.delete_files.return_value = expected_result
        
        # Import the delete_files function
        from agent_server.core.api.rag import delete_files
        from agent_server.core.api.rag import FileBatchDeleteRequest
        
        # Create request object
        request = FileBatchDeleteRequest(file_ids=self.test_file_ids)
        
        # Call the API function
        result = await delete_files(request, self.test_user)
        
        # Verify the response reflects partial failure
        self.assertEqual(result.code, 200)
        self.assertEqual(result.data.deleted_files, ["file1"])
        self.assertEqual(len(result.data.failed_files), 1)
        self.assertEqual(result.data.total_deleted, 1)
        self.assertEqual(result.data.total_failed, 1)
        self.assertIn("1 succeeded, 1 failed", result.message)
    
    @patch('core.api.rag.get_rag_service')
    @patch('core.api.rag.check_user')
    async def test_delete_files_handles_service_exception(self, mock_check_user, mock_get_rag_service):
        """Test that the API endpoint handles service exceptions correctly"""
        # Mock user authentication
        mock_check_user.return_value = self.test_user
        
        # Mock RAG service to raise an exception
        mock_rag_service = AsyncMock()
        mock_get_rag_service.return_value = mock_rag_service
        mock_rag_service.delete_files.side_effect = Exception("Service error")
        
        # Import the delete_files function
        from agent_server.core.api.rag import delete_files
        from agent_server.core.api.rag import FileBatchDeleteRequest
        
        # Create request object
        request = FileBatchDeleteRequest(file_ids=self.test_file_ids)
        
        # Call the API function and expect HTTPException
        with self.assertRaises(HTTPException) as context:
            await delete_files(request, self.test_user)
        
        self.assertEqual(context.exception.status_code, 500)
        self.assertEqual(context.exception.detail, "Failed to delete files")


if __name__ == "__main__":
    unittest.main()
