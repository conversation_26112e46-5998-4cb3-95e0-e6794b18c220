# Include documentation
include README.md
include LICENSE
include CHANGELOG.md

# Include configuration files
recursive-include config *.yml *.yaml *.json
recursive-include core/config *.yml *.yaml *.json

# Include database migrations
recursive-include migrations *.py *.mako
recursive-include core/services/database/migrations *.py *.mako

# Include templates and static files
recursive-include templates *.html *.jinja2
recursive-include static *.css *.js *.png *.jpg *.ico

# Include test data
recursive-include tests/data *.json *.yml *.yaml

# Exclude development files
exclude .env*
exclude .gitignore
exclude .dockerignore
exclude Dockerfile*
exclude docker-compose*.yml
exclude pytest.ini
exclude .coverage
exclude .pytest_cache
recursive-exclude * __pycache__
recursive-exclude * *.py[co]
recursive-exclude * .DS_Store
