
# -*- coding: utf-8 -*-
"""
@File    :   MdTemplate.py
@Time    :   2025/06/21
<AUTHOR>   <PERSON> (h<PERSON><PERSON><PERSON>@chinacscs.com)
@Version :   0.0.10
@Desc    :   Markdown Prompts Template manager 
"""

import dataclasses
from datetime import datetime
from typing import Any, List, Union
from pydantic import BaseModel
from jinja2 import (
    Environment, 
    FileSystemLoader,
    ChoiceLoader,
    select_autoescape, 
    Undefined
)
from agent_server.core.config.app_logger import logger
import traceback
class KeepVariablesUndefined(Undefined):
    """保留未填充变量为原始语法形式"""
    def __str__(self):
        return f"{{{self._undefined_name}}}" if self._undefined_name else ""
    
    def __getattr__(self, name):
        return KeepVariablesUndefined(name=f"{self._undefined_name}.{name}")
    
    def __getitem__(self, key):
        return KeepVariablesUndefined(name=f"{self._undefined_name}[{key!r}]")

from typing import Optional
from jinja2 import BaseLoader, TemplateNotFound

class DatabaseLoader(BaseLoader):
    """从数据库加载模板的自定义加载器"""
    def __init__(self, db_session=None):
        self.db_session = db_session
    
    def get_source(self, environment, template):
        """
        从数据库获取模板内容
        :param template: 模板文件名(如: template_name.md)
        """
        #from models import PromptTemplates  
        template_name = template.replace('.md', '')
        template_record = None
        # template_record = self.db_session.query(PromptTemplates).filter_by(
        #     name=template_name, 
        #     format='md'
        # ).first()
        
        if not template_record:
            raise TemplateNotFound(template)
            
        return template_record.content, None, lambda: True


class MdTemplate:
    def __init__(
        self, 
        default_prompt_dir: str, 
        customized_prompt_dirs: Union[str, List[dir]] = None,
        db_session = None
        ):
        self.default_prompt_dir = default_prompt_dir
        self.customized_prompt_dirs = customized_prompt_dirs
        
        loaders = []

        # 优先添加数据库加载器
        if db_session:
            loaders.append(DatabaseLoader(db_session))

        if customized_prompt_dirs:
            if isinstance(customized_prompt_dirs, str):
                loaders.append(FileSystemLoader(customized_prompt_dirs))
            elif isinstance(customized_prompt_dirs, list):
                loaders.extend([FileSystemLoader(dir) for dir in customized_prompt_dirs])
            else:
                raise ValueError("customized_prompt_dirs must be a string or a list of strings")
        loaders.append(FileSystemLoader(default_prompt_dir))
        choice_loader = ChoiceLoader(loaders)

        # Initialize Jinja2 environment
        self.env = Environment(
            loader=choice_loader,
            autoescape=select_autoescape(),
            trim_blocks=True,
            lstrip_blocks=True,
        )
        self.env = self.env.overlay(undefined=KeepVariablesUndefined)


    def get_prompt_template(
        self,
        prompt_name: str
    ) -> str:
        """
        Load and return a prompt template using Jinja2.

        Args:
            prompt_name: Name of the prompt template file (without .md extension)

        Returns:
            The template string with proper variable substitution syntax
        """
        try:
            template = self.env.get_template(f"{prompt_name}.md")
            return template.render()

        except Exception as e:
            raise ValueError(f"Error loading template {prompt_name}: {e}")


    def apply_prompt_template(
        self, 
        prompt_name: str, 
        state: BaseModel | dict | None = None, 
        configurable: Any = None,
        **kwargs
    ) -> str:
        """
        Apply template variables to a prompt template and return formatted string.
        If some variables are not provided, they will be replaced with {variable_name} in the python string format. This is useful for partial rendering.

        Args:
            prompt_name: Name of the prompt template to use
            state: Current agent state containing variables to substitute
            configurable: Configurable variables
            **kwargs: Additional keyword arguments to substitute in the template

        Returns:
            The formatted prompt as a string
        """
        # Convert state to dict for template rendering
        state_dict = state.model_dump() if isinstance(state, BaseModel) else state
        state_dict = state_dict or {}
        configurable_dict = dataclasses.asdict(configurable) if dataclasses.is_dataclass(configurable) else configurable
        configurable_dict = configurable_dict or {}
        state_vars = {
            "CURRENT_TIME": datetime.now().strftime("%a %b %d %Y %H:%M:%S %z"),
            **state_dict,
            **configurable_dict,
            **kwargs
        }

        try:
            template = self.env.get_template(f"{prompt_name}.md")
            system_prompt = template.render(**state_vars)
            return system_prompt
        except Exception as e:
            logger.warning(f"应用模版提示词失败: {str(e)}")
            traceback.print_exc()
            raise ValueError(f"Error applying template {prompt_name}: {e}")
