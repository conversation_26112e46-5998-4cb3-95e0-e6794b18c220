from typing import List, Optional
from sqlmodel import select, func
from sqlalchemy.ext.asyncio import AsyncSession
from agent_server.core.services.database.schemas.user_uploaded_files import (
    UserUploadedFilesTable,
    UserUploadedFilesCreate,
    UserUploadedFilesUpdate,
    FileStatus
)


class UserUploadedFilesCRUD:
    """用户上传文件的CRUD操作类"""

    async def create(
            self,
            db: AsyncSession,
            *,
            obj_in: UserUploadedFilesCreate
    ) -> UserUploadedFilesTable:
        """创建新的文件上传记录"""
        db_obj = UserUploadedFilesTable.model_validate(obj_in)
        db.add(db_obj)
        await db.commit()
        await db.refresh(db_obj)
        return db_obj

    async def get(
            self,
            db: AsyncSession,
            id: str
    ) -> Optional[UserUploadedFilesTable]:
        """根据ID获取文件记录"""
        statement = select(UserUploadedFilesTable).where(UserUploadedFilesTable.id == id)
        result = await db.execute(statement)
        return result.first()
    
    async def get_by_ids(
            self,
            db: AsyncSession,
            ids: List[str]
    ) -> Optional[UserUploadedFilesTable]:
        """根据ID获取文件记录"""
        statement = select(UserUploadedFilesTable).where(UserUploadedFilesTable.id.in_(ids))
        result = await db.execute(statement)
        return result.all()

    async def get_by_user_id(
            self,
            db: AsyncSession,
            user_id: int,
            skip: int = 0,
            limit: int = 100,
            status: Optional[FileStatus] = None
    ) -> List[UserUploadedFilesTable]:
        """根据用户ID获取文件列表"""
        statement = (
            select(UserUploadedFilesTable)
            .where(UserUploadedFilesTable.user_id == user_id)
        )

        if status is not None:
            statement = statement.where(UserUploadedFilesTable.status == status)

        statement = (
            statement
            .order_by(UserUploadedFilesTable.created_at.desc())
            .offset(skip)
            .limit(limit)
        )
        result = await db.execute(statement)
        return result.all()

    async def get_by_file_hash(
            self,
            db: AsyncSession,
            file_hash: str,
            user_id: Optional[int] = None
    ) -> Optional[UserUploadedFilesTable]:
        """根据文件哈希值获取文件记录（可选择性过滤用户）"""
        statement = select(UserUploadedFilesTable).where(
            UserUploadedFilesTable.hash == file_hash
        )
        if user_id is not None:
            statement = statement.where(UserUploadedFilesTable.user_id == user_id)

        result = await db.execute(statement)
        return result.first()

    async def get_by_rag_file_id(
            self,
            db: AsyncSession,
            rag_file_id: str
    ) -> Optional[UserUploadedFilesTable]:
        """根据RAGFlow文档ID获取文件记录"""
        statement = select(UserUploadedFilesTable).where(
            UserUploadedFilesTable.rag_file_id == rag_file_id
        )
        result = await db.execute(statement)
        return result.first()

    async def get_by_status(
            self,
            db: AsyncSession,
            status: FileStatus,
            user_id: Optional[int] = None,
            skip: int = 0,
            limit: int = 100
    ) -> List[UserUploadedFilesTable]:
        """根据状态获取文件列表"""
        statement = select(UserUploadedFilesTable).where(
            UserUploadedFilesTable.status == status
        )
        if user_id is not None:
            statement = statement.where(UserUploadedFilesTable.user_id == user_id)

        statement = statement.order_by(UserUploadedFilesTable.created_at.desc()).offset(skip).limit(limit)
        result = await db.execute(statement)
        return result.all()

    async def update(
            self,
            db: AsyncSession,
            *,
            db_obj: UserUploadedFilesTable,
            obj_in: UserUploadedFilesUpdate
    ) -> UserUploadedFilesTable:
        """更新文件记录"""
        obj_data = obj_in.model_dump(exclude_unset=True)
        for field, value in obj_data.items():
            setattr(db_obj, field, value)

        db.add(db_obj)
        await db.commit()
        await db.refresh(db_obj)
        return db_obj

    async def update_status(
            self,
            db: AsyncSession,
            *,
            file_id: str,
            status: FileStatus,
            error_message: Optional[str] = None
    ) -> Optional[UserUploadedFilesTable]:
        """更新文件状态"""
        db_obj = await self.get(db, file_id)
        if db_obj:
            update_data = UserUploadedFilesUpdate(
                status=status,
                error_message=error_message
            )
            return await self.update(db, db_obj=db_obj, obj_in=update_data)
        return None

    async def delete(
            self,
            db: AsyncSession,
            *,
            id: str
    ) -> Optional[UserUploadedFilesTable]:
        """删除文件记录"""
        db_obj = await self.get(db, id)
        if db_obj:
            await db.delete(db_obj)
            await db.commit()
        return db_obj

    async def count_by_user(
            self,
            db: AsyncSession,
            user_id: int
    ) -> int:
        """统计用户上传的文件数量"""
        statement = select(func.count(UserUploadedFilesTable.id)).where(
            UserUploadedFilesTable.user_id == user_id
        )
        result = await db.execute(statement)
        return result.one()

    async def count_by_user_id(
            self,
            db: AsyncSession,
            user_id: int,
            status: Optional[FileStatus] = None
    ) -> int:
        """统计用户上传的文件数量（支持状态过滤）"""
        statement = select(func.count(UserUploadedFilesTable.id)).where(
            UserUploadedFilesTable.user_id == user_id
        )

        if status is not None:
            statement = statement.where(UserUploadedFilesTable.status == status)

        result = await db.execute(statement)
        return result.one()

    async def delete_by_rag_file_id(
            self,
            db: AsyncSession,
            rag_file_id: str
    ) -> bool:
        """根据RAG文件ID删除文件记录"""
        db_obj = await self.get_by_rag_file_id(db, rag_file_id=rag_file_id)
        if db_obj:
            await db.delete(db_obj)
            await db.commit()
            return True
        return False

    async def get_rag_file_ids_by_user_file_ids(
            self,
            db: AsyncSession,
            user_file_ids: List[str],
            user_id: int
    ) -> List[str]:
        """
        根据用户文件ID列表获取对应的RAG文件ID列表

        Args:
            db: 数据库会话
            user_file_ids: 用户文件ID列表（user_uploaded_files表的id字段）
            user_id: 用户ID，用于权限验证

        Returns:
            List[str]: 对应的RAG文件ID列表
        """
        statement = select(UserUploadedFilesTable.rag_file_id).where(
            UserUploadedFilesTable.id.in_(user_file_ids),
            UserUploadedFilesTable.user_id == user_id
        )
        result = await db.execute(statement)
        return [item[0] for item in result.all()]


# 创建CRUD实例
user_uploaded_files_crud = UserUploadedFilesCRUD()
