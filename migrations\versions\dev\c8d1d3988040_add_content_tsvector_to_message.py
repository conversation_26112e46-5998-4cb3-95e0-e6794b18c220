"""add_content_tsvector_to_message

Revision ID: c8d1d3988040
Revises: 0443ef4e0e90
Create Date: 2025-07-13 23:21:30.491610

"""
from typing import Sequence, Union

from alembic import op
import sqlalchemy as sa
from sqlalchemy.dialects import postgresql
import sqlmodel

# revision identifiers, used by Alembic.
revision: str = 'c8d1d3988040'
down_revision: Union[str, None] = '0443ef4e0e90'
branch_labels: Union[str, Sequence[str], None] = None
depends_on: Union[str, Sequence[str], None] = None


def upgrade() -> None:
    """Upgrade schema."""
    # 添加 content_tsvector 字段
    op.add_column('message', sa.Column('content_tsvector', postgresql.TSVECTOR(), nullable=True))

    # 创建 GIN 索引以提高全文搜索性能
    op.create_index(
        'idx_message_content_tsvector',
        'message',
        ['content_tsvector'],
        postgresql_using='gin'
    )

    # 创建触发器函数，用于自动更新 tsvector 字段
    op.execute("""
        CREATE OR REPLACE FUNCTION update_message_content_tsvector()
        RETURNS TRIGGER AS $$
        BEGIN
            -- 从 content JSONB 字段中提取文本内容并生成 tsvector
            NEW.content_tsvector := to_tsvector('simple',
                COALESCE(
                    (
                        SELECT string_agg(
                            CASE
                                WHEN item->>'type' = 'text' THEN item->>'text'
                                WHEN item->>'type' = 'tool_use' THEN item->>'name'
                                ELSE ''
                            END,
                            ' '
                        )
                        FROM jsonb_array_elements(NEW.content) AS item
                    ),
                    ''
                )
            );
            RETURN NEW;
        END;
        $$ LANGUAGE plpgsql;
    """)

    # 创建触发器
    op.execute("""
        CREATE TRIGGER trigger_update_message_content_tsvector
        BEFORE INSERT OR UPDATE ON message
        FOR EACH ROW
        EXECUTE FUNCTION update_message_content_tsvector();
    """)

    # 为现有数据更新 tsvector 字段
    op.execute("""
        UPDATE message
        SET content_tsvector = to_tsvector('simple',
            COALESCE(
                (
                    SELECT string_agg(
                        CASE
                            WHEN item->>'type' = 'text' THEN item->>'text'
                            WHEN item->>'type' = 'tool_use' THEN item->>'name'
                            ELSE ''
                        END,
                        ' '
                    )
                    FROM jsonb_array_elements(content) AS item
                ),
                ''
            )
        )
        WHERE content_tsvector IS NULL;
    """)


def downgrade() -> None:
    """Downgrade schema."""
    # 删除触发器
    op.execute("DROP TRIGGER IF EXISTS trigger_update_message_content_tsvector ON message;")

    # 删除触发器函数
    op.execute("DROP FUNCTION IF EXISTS update_message_content_tsvector();")

    # 删除索引
    op.drop_index('idx_message_content_tsvector', table_name='message')

    # 删除字段
    op.drop_column('message', 'content_tsvector')
