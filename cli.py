#!/usr/bin/env python3
"""
Agent Server CLI

Command line interface for the Agent Server framework.
"""

import argparse
import asyncio
import sys
from pathlib import Path

def main():
    """Main CLI entry point"""
    parser = argparse.ArgumentParser(
        description="Agent Server - AI Agent Framework",
        formatter_class=argparse.RawDescriptionHelpFormatter,
        epilog="""
Examples:
  agent-server run                    # Start the server
  agent-server init                   # Initialize configuration
  agent-server migrate                # Run database migrations
  agent-server --version              # Show version
        """
    )
    
    parser.add_argument(
        "--version", 
        action="version", 
        version="%(prog)s 1.0.0"
    )
    
    parser.add_argument(
        "--config",
        default="config/application.yml",
        help="Configuration file path (default: config/application.yml)"
    )
    
    subparsers = parser.add_subparsers(dest="command", help="Available commands")
    
    # Run command
    run_parser = subparsers.add_parser("run", help="Start the agent server")
    run_parser.add_argument(
        "--host", 
        default="0.0.0.0", 
        help="Host to bind (default: 0.0.0.0)"
    )
    run_parser.add_argument(
        "--port", 
        type=int, 
        default=8000, 
        help="Port to bind (default: 8000)"
    )
    run_parser.add_argument(
        "--reload", 
        action="store_true", 
        help="Enable auto-reload for development"
    )
    
    # Init command
    init_parser = subparsers.add_parser("init", help="Initialize configuration")
    init_parser.add_argument(
        "--force", 
        action="store_true", 
        help="Overwrite existing configuration"
    )
    
    # Migrate command
    migrate_parser = subparsers.add_parser("migrate", help="Run database migrations")
    migrate_parser.add_argument(
        "--upgrade", 
        action="store_true", 
        help="Upgrade to latest migration"
    )
    
    args = parser.parse_args()
    
    if not args.command:
        parser.print_help()
        return
    
    # Set config path environment variable
    import os
    os.environ["CONFIG_PATH"] = args.config
    
    if args.command == "run":
        run_server(args)
    elif args.command == "init":
        init_config(args)
    elif args.command == "migrate":
        run_migrations(args)

def run_server(args):
    """Start the agent server"""
    try:
        import uvicorn
        from main import app
        
        uvicorn.run(
            "main:app",
            host=args.host,
            port=args.port,
            reload=args.reload,
            log_level="info"
        )
    except ImportError:
        print("Error: uvicorn is required to run the server")
        sys.exit(1)
    except Exception as e:
        print(f"Error starting server: {e}")
        sys.exit(1)

def init_config(args):
    """Initialize configuration files"""
    config_path = Path(args.config)
    
    if config_path.exists() and not args.force:
        print(f"Configuration file {config_path} already exists. Use --force to overwrite.")
        return
    
    # Create config directory if it doesn't exist
    config_path.parent.mkdir(parents=True, exist_ok=True)
    
    # Create sample configuration
    sample_config = """
# Agent Server Configuration
redis:
  host: "127.0.0.1"
  port: 6379
  password: null
  db: 0

database:
  url: "postgresql+asyncpg://postgres:postgres@localhost:5432/agent_db"

business_system:
  host: "0.0.0.0"
  port: 8000

ai:
  models:
    default: "gpt-4"
    gpt-4:
      name: "gpt-4"
      api_key: "your-api-key-here"
      base_url: "https://api.openai.com/v1"
      temperature: 0.0

auth:
  adapter: "core.auth.default_auth_adapter.DefaultAuthAdapter"
  auth_url: "http://localhost:8080/auth"

agents: []
mcp_servers: []
"""
    
    with open(config_path, 'w') as f:
        f.write(sample_config.strip())
    
    print(f"Configuration file created at {config_path}")
    print("Please edit the configuration file with your settings.")

def run_migrations(args):
    """Run database migrations"""
    try:
        import subprocess
        
        if args.upgrade:
            result = subprocess.run(["alembic", "upgrade", "head"], check=True)
        else:
            result = subprocess.run(["alembic", "current"], check=True)
            
    except subprocess.CalledProcessError as e:
        print(f"Migration failed: {e}")
        sys.exit(1)
    except FileNotFoundError:
        print("Error: alembic is required for database migrations")
        sys.exit(1)

if __name__ == "__main__":
    main()
