#!/usr/bin/env python3
"""
Setup script for agent-server package
"""

from setuptools import setup, find_packages
import os

# Read the contents of README file
# @author: xiangjh
this_directory = os.path.abspath(os.path.dirname(__file__))
with open(os.path.join(this_directory, 'README.md'), encoding='utf-8') as f:
    long_description = f.read()

# Read requirements
def read_requirements(filename):
    with open(filename, 'r', encoding='utf-8') as f:
        return [line.strip() for line in f if line.strip() and not line.startswith('#')]

setup(
    name="agent-server",
    version="1.0.0",
    author="xiangjh",
    author_email="<EMAIL>",
    description="A comprehensive AI agent framework with LangChain, LangGraph, and MCP support",
    long_description=long_description,
    long_description_content_type="text/markdown",
    url="https://github.com/yourusername/agent-server",
    packages=find_packages(exclude=["tests*"],include=["agent_server*", "agent_rags*", "agent_system*"]),
    python_requires=">=3.11",
    entry_points={
        "console_scripts": [
            "agent-server=cli:main",
        ],
    },
    include_package_data=True,
    zip_safe=False,
    keywords="ai agent langchain langgraph mcp llm",
    project_urls={
        "Bug Reports": "https://github.com/yourusername/agent-server/issues",
        "Source": "https://github.com/yourusername/agent-server",
        "Documentation": "https://agent-server.readthedocs.io/",
    },
)
