from agent_server.core.base.langchain_sender import LangchainBaseSender
import asyncio
import re
from typing import Dict, Any, AsyncIterator
import json
from agent_server.core.config.app_logger import logger
from agent_server.core.services.database.base import DatabaseManager
from agent_server.mcpserver.mcp_server import save_page_config, check_page_config
from agent_server.core.services.database.crud.message import message_curd
from agent_server.core.config.app_constant import AppConstant
from agent_server.core.config.app_config import config


################################
# 动态页面智能体
################################
class PageCreatorSender(LangchainBaseSender):
    """动态页面创建专用发送器"""

    async def _latest_msg_dynamicid(self) -> str:
        # 获取最近的有效配置
        dynamic_id = None
        try:
            session = DatabaseManager.createScopedSession()

            # 添加调试日志 - 验证conversation_id是否正确
            logger.debug(f"当前conversation_id: {self.transmitter.conversation_id}")

            latest_msg = message_curd.get_latest_valid_data_object_sync(
                session, conversation_id=self.transmitter.conversation_id
            )
            
            if latest_msg and (data := latest_msg.data_object):
                dynamic_id = data.get("dynamicId", "")

            logger.warning(f"操作对象dynamicId: {dynamic_id}")
        except Exception as e:
            print(f"Error: {e}")
        finally:
            if session:
                session.remove()
        return dynamic_id

    async def _build_final_question(self, raw_question: str) -> str:
        return raw_question + "，并用中文回答"

    def _handle_stream_conten(self, chunk: Dict[str, Any]) -> dict:
        _package_type = chunk.get("package_type", AppConstant.DEFAULT_PACKAGE_TYPE)
        return self._wrap_package(chunk.get("content"), _package_type)

    async def _handle_response(self, chunk: Dict[str, Any]) -> AsyncIterator[Any]:
        """处理响应类型chunk并流式返回结果"""
        _content = chunk.get("content", "")
        json_match = re.search(r"```json[\r\n]+(.*?)[\r\n]+```", _content, re.DOTALL)
        if not json_match:
            return

        yield {
            "data": " ",
            "package_type": AppConstant.DEFAULT_PACKAGE_TYPE,
            "is_last": True,
            "is_new_package": False,
        }
        try:
            json_str = json_match.group(1).strip()
            config_json = json.loads(json_str)
            logger.debug(f"解析到配置JSON: {config_json}")

            if not config_json.get("intention"):
                return

            services = config.full_config.get("gateway").get("services")
            target_url = None
            for service in services:
                if service.get("name") == "cluster":
                    target_url = service.get("url")
                    break

            if not target_url:
                raise Exception("未配置cluster服务url")

            config_json["conversationId"] = self.transmitter.conversation_id
            target_url = target_url+"/page/system/dynamic/page/manage/insertSqlWithTempForAi"

            if config_json.get("intention") == "query":
                yield {"data": "", "is_new_package": True, "package_type": 0}
                save_result = await save_page_config(
                    kwargs={"config_json": config_json, "target_url": target_url}
                )
                object_reulst = json.loads(save_result)
                logger.warning(f"save_page_config: {object_reulst}")
                await asyncio.sleep(0.5)
                if object_reulst.get("success"):
                    dynamicId = object_reulst["data"].get("dynamicId")
                    dynamicName = object_reulst["data"].get("dynamicName")
                    dynamic_data = {
                        "dynamicId": dynamicId,
                    }

                    self.transmitter._set_data_obj(dynamic_data)
                    async for char in self._generate_content_stream(
                        AppConstant.Dynamic.OPERATION_COMPLETE
                    ):
                        yield char
                    yield self._generate_preview_widget(dynamicId, dynamicName)
                else:
                    err_msg = object_reulst.get("errorMessage", AppConstant.OTHER_ERROR)
                    logger.error(err_msg)
                    yield self._handle_tool_result({
                        "tool": AppConstant.Dynamic.SAVE_CONFIG,
                        "result": "faild",
                        "tool_msg": err_msg,
                    })
                    yield AppConstant.Dynamic.SAVE_FAILED
                return
            else:
                yield {
                    "data": AppConstant.Dynamic.TOOL_CALL_START,
                    "is_new_package": True,
                    "package_type": 0,
                }
                validate_result = check_page_config(kwargs={"config_json": config_json})
                result_data = json.loads(validate_result)

                tool_start = {
                    "type": "tool_start",
                    "tool": AppConstant.Dynamic.CHECK_CONFIG,
                }
                yield super()._handle_tool_start(tool_start)
                await asyncio.sleep(1.5)

                if not result_data.get("success"):
                    yield self._handle_tool_result({
                        "tool": AppConstant.Dynamic.CHECK_CONFIG,
                        "result": "faild",
                    })
                    yield f"验证错误: {result_data.get('resultMessage', AppConstant.OTHER_ERROR)}"
                    return

                yield self._handle_tool_result({
                    "tool": AppConstant.Dynamic.CHECK_CONFIG,
                    "result": "success",
                })

                yield self._handle_tool_start({
                    "type": "tool_start",
                    "tool": AppConstant.Dynamic.SAVE_CONFIG,
                })
                validated_config = result_data.get("config_json", {})

                if validated_config.get("intention") == "update":
                    last_dy_id = await self._latest_msg_dynamicid()
                    if last_dy_id:
                        validated_config["dynamicId"] = last_dy_id

                await asyncio.sleep(0.5)
                logger.info(f"save_page_config：{validated_config}")
                save_result = await save_page_config(
                    kwargs={"config_json": validated_config, "target_url": target_url}
                )
                object_reulst = json.loads(save_result)
                logger.warning(f"save_page_config: {object_reulst}")
                await asyncio.sleep(0.5)
                if object_reulst.get("success"):
                    dynamicId = object_reulst["data"].get("dynamicId")
                    dynamicName = object_reulst["data"].get("dynamicName")
                    dynamic_data = {
                        "dynamicId": dynamicId,
                    }

                    self.transmitter._set_data_obj(dynamic_data)
                    yield self._handle_tool_result({
                        "tool": AppConstant.Dynamic.SAVE_CONFIG,
                        "result": "success",
                    })
                    async for char in self._generate_content_stream(
                        AppConstant.Dynamic.OPERATION_COMPLETE
                    ):
                        yield char
                    yield self._generate_preview_widget(dynamicId, dynamicName)
                else:
                    err_msg = object_reulst.get("errorMessage", AppConstant.OTHER_ERROR)
                    logger.error(err_msg)
                    yield self._handle_tool_result({
                        "tool": AppConstant.Dynamic.SAVE_CONFIG,
                        "result": "faild",
                        "tool_msg": err_msg,
                    })
                    yield AppConstant.Dynamic.SAVE_FAILED

        except json.JSONDecodeError as e:
            yield AppConstant.JSON_ERROR.format(error=str(e))
        except Exception as e:
            err_msg = AppConstant.PROCESS_ERROR.format(error=str(e))
            logger.error(err_msg)
            yield self._handle_tool_result({
                "tool": AppConstant.Dynamic.SAVE_CONFIG,
                "result": "faild",
            })
            yield err_msg
