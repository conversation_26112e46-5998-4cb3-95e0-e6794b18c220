#!/usr/bin/env python3
"""
真实 LLM 调用的 Human-in-the-Loop 测试
使用真实的 LLM 配置进行酒店预订工具测试
"""

import sys
import os
import asyncio

from langgraph.types import Command

# 添加项目根目录到路径
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

from langchain_core.tools import tool
from langchain_core.messages import HumanMessage
from langchain_openai import ChatOpenAI
from langgraph.prebuilt import create_react_agent
from langgraph.checkpoint.memory import MemorySaver
from langgraph.types import Command
from agent_server.core.langgraph.graph_builder import BaseGraphBuilder

# <AUTHOR> xiangjh
class WrapToolForHilTest:
    """WrapToolForHilTest 测试类"""
    
    def __init__(self):
        self.graph_builder = TestGraphBuilder()
        self.checkpointer = MemorySaver()
        
        # 真实 LLM 配置
        self.llm = ChatOpenAI(
            model="volcengine/deepseek-v3",
            openai_api_key="sk-giZY3EQGWzUhpuyslUfl5KWIv7wPh0nFul0k56wems7NnTXy",
            openai_api_base="http://172.17.9.46:3000/v1",
            temperature=0
        )
        
    def create_hotel_tool(self):
        """创建酒店预订工具"""
        @tool
        def book_hotel(city: str, checkin_date: str = "2024-01-15", nights: int = 1) -> str:
            """预订酒店
            
            Args:
                city: 城市名称
                checkin_date: 入住日期 (YYYY-MM-DD)
                nights: 住宿天数
                
            Returns:
                str: 预订结果
            """
            return f"✅ 已成功预订 {city} 的酒店，入住日期：{checkin_date}，住宿 {nights} 晚"
        
        return book_hotel
    
    async def test_accept(self):
        """测试用户接受工具调用的流程"""
        print("🧪 测试1: 用户接受工具调用")
        print("-" * 50)

        try:
            # 1. 创建和包装工具
            hotel_tool = self.create_hotel_tool()
            wrapped_tool = self.graph_builder.wrap_tool_for_hil(hotel_tool)
            print(f"✅ 工具包装成功: {wrapped_tool.name}")

            # 2. 创建 React Agent
            agent = create_react_agent(
                model=self.llm,
                tools=[wrapped_tool],
                checkpointer=self.checkpointer
            )
            print("✅ React Agent 创建成功")

            config = {"configurable": {"thread_id": "accept-test"}}

            # 3. 第一次调用 - 触发中断
            print("🤖 发送用户请求: '帮我预订北京的酒店'")


            try:
                response = await agent.ainvoke(
                    {"messages": [HumanMessage(content="帮我预订北京的酒店")]},
                    config=config
                )
                print("⏸️ 已触发中断...等待恢复")
                self.analyze_response(response, "接受(中断)")
            except Exception as interrupt_error:
                print(f"✅ 中断触发: {str(interrupt_error)[:100]}...")

            # 4. 第二次调用 - 使用 Command 恢复
            print("🔄 使用 Command 恢复执行 - 用户接受")
            human_response = [{"type": "accept", "args": {}}]

            response = await agent.ainvoke(
                Command(resume=human_response),
                config=config
            )

            print("✅ Agent 恢复执行完成")
            print(f"   消息数量: {len(response['messages'])}")

            # 分析响应
            self.analyze_response(response, "接受")

            return True

        except Exception as e:
            print(f"❌ 测试失败: {e}")
            import traceback
            traceback.print_exc()
            return False
    
    async def test_edit(self):
        """测试用户编辑参数的流程"""
        print("\n🧪 测试2: 用户编辑工具参数")
        print("-" * 50)
        
        try:
            # 1. 创建和包装工具
            hotel_tool = self.create_hotel_tool()
            wrapped_tool = self.graph_builder.wrap_tool_for_hil(hotel_tool)
            
            # 2. 创建 React Agent
            agent = create_react_agent(
                model=self.llm,
                tools=[wrapped_tool],
                checkpointer=self.checkpointer
            )
            
            config = {"configurable": {"thread_id": "edit-test"}}

            # 3. 第一次调用 - 触发中断
            print("🤖 发送用户请求: '帮我预订北京的酒店，住2晚'")

            try:
                response = await agent.ainvoke(
                    {"messages": [HumanMessage(content="帮我预订北京的酒店，住2晚")]},
                    config=config
                )
                print("⏸️ 已触发中断...等待恢复")
                self.analyze_response(response, "编辑(未中断)")
            except Exception as interrupt_error:
                print(f"✅ 中断触发: {str(interrupt_error)[:100]}...")

            # 4. 第二次调用 - 使用 Command 恢复，编辑参数
            print("🔄 使用 Command 恢复执行 - 用户编辑参数")
            human_response = [{
                "type": "edit",
                "args": {
                    "args": {
                        "city": "上海",
                        "checkin_date": "2024-02-01",
                        "nights": 3
                    }
                }
            }]

            response = await agent.ainvoke(
                Command(resume=human_response),
                config=config
            )

            print("✅ Agent 恢复执行完成")

            # 分析响应
            self.analyze_response(response, "编辑")

            # 检查是否使用了编辑后的参数
            tool_messages = [msg for msg in response['messages']
                           if hasattr(msg, 'name') and msg.name == 'book_hotel']

            if tool_messages and "上海" in tool_messages[0].content:
                print("✅ 参数编辑成功 - 城市已修改为上海")
            else:
                print("⚠️ 参数编辑可能未完全生效")

            return True
                
        except Exception as e:
            print(f"❌ 测试失败: {e}")
            import traceback
            traceback.print_exc()
            return False
    
    async def test_respond_flow(self):
        """测试用户直接响应的流程"""
        print("\n🧪 测试3: 用户直接提供响应")
        print("-" * 50)
        
        try:
            # 1. 创建和包装工具
            hotel_tool = self.create_hotel_tool()
            wrapped_tool = self.graph_builder.wrap_tool_for_hil(hotel_tool)
            
            # 2. 创建 React Agent
            agent = create_react_agent(
                model=self.llm,
                tools=[wrapped_tool],
                checkpointer=self.checkpointer
            )
            
            config = {"configurable": {"thread_id": "respond-test"}}

            # 3. 第一次调用 - 触发中断
            print("🤖 发送用户请求: '帮我预订北京的酒店'")
            print("⏸️ 等待人工中断触发...")

            try:
                response = await agent.ainvoke(
                    {"messages": [HumanMessage(content="帮我预订北京的酒店")]},
                    config=config
                )
                print("⚠️ 未触发中断，直接执行完成")
                self.analyze_response(response, "直接响应(未中断)")
                return True
            except Exception as interrupt_error:
                print(f"✅ 中断触发: {str(interrupt_error)[:100]}...")

            # 4. 第二次调用 - 使用 Command 恢复，直接响应
            print("🔄 使用 Command 恢复执行 - 用户直接响应")
            custom_response = "抱歉，由于春节期间酒店紧张，北京的酒店暂时无法预订。建议您选择其他城市或延后出行日期。"
            human_response = [{
                "type": "respond",
                "args": custom_response
            }]

            response = await agent.ainvoke(
                Command(resume=human_response),
                config=config
            )

            print("✅ Agent 恢复执行完成")

            # 分析响应
            self.analyze_response(response, "直接响应")

            # 检查是否使用了用户提供的响应
            tool_messages = [msg for msg in response['messages']
                           if hasattr(msg, 'name') and msg.name == 'book_hotel']

            if tool_messages and "春节期间" in tool_messages[0].content:
                print("✅ 用户直接响应成功 - 使用了自定义回复")
            else:
                print("⚠️ 用户响应可能未生效")
                
                return True
                
        except Exception as e:
            print(f"❌ 测试失败: {e}")
            import traceback
            traceback.print_exc()
            return False
    
    def analyze_response(self, response, test_type):
        """分析 Agent 响应"""
        print(f"\n📊 {test_type} -测试结果分析:")
        print("-" * 30)
        
        messages = response['messages']
        print(f"总消息数: {len(messages)}")
        
        for i, msg in enumerate(messages):
            msg_type = type(msg).__name__
            print(f"\n消息 {i+1}: {msg_type}")
            
            # if hasattr(msg, 'content'):
            #     content = msg.content[:150] + "..." if len(msg.content) > 150 else msg.content
            #     print(f"  内容: {content}")
            #
            # if hasattr(msg, 'tool_calls') and msg.tool_calls:
            #     print(f"  工具调用: {msg.tool_calls}")
            #
            # if hasattr(msg, 'name'):
            #     print(f"  工具名称: {msg.name}")
            print(f"  消息内容: {msg}")
    
    async def run_all_tests(self):
        """运行所有测试"""
        print("🚀 真实 LLM 调用 HIL 测试开始")
        print("=" * 60)
        print(f"LLM 模型: {self.llm.model_name}")
        print(f"API 地址: {self.llm.openai_api_base}")
        print()
        
        tests = [
            #("用户接受", self.test_accept),
            ("参数编辑", self.test_edit),
            #("直接响应", self.test_respond_flow),
        ]
        
        results = []
        for test_name, test_func in tests:
            try:
                result = await test_func()
                results.append((test_name, result))
            except Exception as e:
                print(f"❌ 测试 {test_name} 异常: {e}")
                results.append((test_name, False))
        
        # 输出结果汇总
        print("\n📊 测试结果汇总")
        print("=" * 60)
        
        success_count = 0
        for test_name, success in results:
            status = "✅ 通过" if success else "❌ 失败"
            print(f"{test_name}: {status}")
            if success:
                success_count += 1
        
        print(f"\n🎯 总结: {success_count}/{len(results)} 测试通过")
        
        return success_count == len(results)


class TestGraphBuilder(BaseGraphBuilder):
    """测试用的 GraphBuilder"""
    
    def build_langgraph_agent(self, agent_id: str, tools, model_config):
        pass


async def main():
    """主函数"""
    test = WrapToolForHilTest()
    success = await test.run_all_tests()
    
    if success:
        print("\n🎊 测试完全成功！")
    else:
        print("\n🔧 需要进一步调试和优化")


if __name__ == "__main__":
    asyncio.run(main())
