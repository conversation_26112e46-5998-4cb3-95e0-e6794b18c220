from typing import Any, Dict, Generic, List, Optional, Type, TypeVar, Union
from sqlalchemy.ext.asyncio import AsyncSession
from sqlmodel import select, SQLModel
from pydantic import BaseModel

ModelType = TypeVar("ModelType", bound=SQLModel)
CreateSchemaType = TypeVar("CreateSchemaType", bound=BaseModel)
UpdateSchemaType = TypeVar("UpdateSchemaType", bound=BaseModel)


class CRUDBase(Generic[ModelType, CreateSchemaType, UpdateSchemaType]):
    def __init__(self, model: Type[ModelType]):
        """
        CRUD基类，提供默认的CRUD操作

        Args:
            model: SQLModel模型类
        """
        self.model = model

    def _has_soft_delete(self) -> bool:
        """检查模型是否支持软删除（是否有is_deleted字段）"""
        return hasattr(self.model, 'is_deleted')

    async def get(self, db: AsyncSession, _id: Any, include_deleted: bool = False) -> Optional[ModelType]:
        """根据ID获取单个对象"""
        query = select(self.model).where(self.model.id == _id)
        if self._has_soft_delete() and not include_deleted:
            query = query.where(self.model.is_deleted.is_(False))
        result = await db.execute(query)
        return result.scalar_one_or_none()

    async def get_multi(
        self, db: AsyncSession, *, skip: int = 0, limit: int = 100, include_deleted: bool = False
    ) -> List[ModelType]:
        """获取多个对象"""
        query = select(self.model).offset(skip).limit(limit)
        if self._has_soft_delete() and not include_deleted:
            query = query.where(self.model.is_deleted.is_(False))
        result = await db.execute(query)
        return result.scalars().all()

    async def create(self, db: AsyncSession, *, obj_input: CreateSchemaType) -> ModelType:
        """创建对象"""
        db_obj = self.model.model_validate(obj_input)
        db.add(db_obj)
        await db.commit()
        await db.refresh(db_obj)
        return db_obj
    
    async def update(
        self,
        db: AsyncSession,
        *,
        db_obj: ModelType,
        obj_input: Union[UpdateSchemaType, Dict[str, Any]],
    ) -> ModelType:
        """更新对象"""
        obj_data = db_obj.dict()

        if isinstance(obj_input, dict):
            update_data = obj_input
        else:
            update_data = obj_input.model_dump(exclude_unset=True)

        for field in obj_data:
            if field in update_data:
                setattr(db_obj, field, update_data[field])

        db.add(db_obj)
        await db.commit()
        await db.refresh(db_obj)

        return db_obj

    async def remove(self, db: AsyncSession, *, _id: Any, hard_delete: bool = False) -> ModelType:
        """删除对象（支持软删除和硬删除）"""
        obj = await self.get(db, _id, include_deleted=True)
        if obj is None:
            return None

        if self._has_soft_delete() and not hard_delete:
            # 执行软删除
            setattr(obj, 'is_deleted', True)
            db.add(obj)
            await db.commit()
            await db.refresh(obj)
        else:
            # 执行硬删除
            await db.delete(obj)
            await db.commit()
        return obj
