"""add_prompt_template_table

Revision ID: f44b52ea782b
Revises: c8d1d3988040
Create Date: 2025-07-14 17:31:34.400067

"""
from typing import Sequence, Union

from alembic import op
import sqlalchemy as sa

import sqlmodel

# revision identifiers, used by Alembic.
revision: str = 'f44b52ea782b'
down_revision: Union[str, None] = 'c8d1d3988040'
branch_labels: Union[str, Sequence[str], None] = None
depends_on: Union[str, Sequence[str], None] = None


def upgrade() -> None:
    """Upgrade schema."""
    # Create prompt_template table
    op.create_table('prompt_template',
    sa.Column('type', sqlmodel.sql.sqltypes.AutoString(), nullable=False),
    sa.Column('title', sqlmodel.sql.sqltypes.AutoString(), nullable=False),
    sa.Column('description', sqlmodel.sql.sqltypes.AutoString(), nullable=False),
    sa.Column('prompt', sqlmodel.sql.sqltypes.AutoString(), nullable=False),
    sa.Column('group', sqlmodel.sql.sqltypes.AutoString(), nullable=False),
    sa.Column('preview', sqlmodel.sql.sqltypes.AutoString(), nullable=False),
    sa.Column('order', sa.Integer(), nullable=False),
    sa.Column('created_at', sa.DateTime(timezone=True), nullable=True),
    sa.Column('updated_at', sa.DateTime(timezone=True), nullable=True),
    sa.Column('id', sa.Integer(), nullable=False),
    sa.PrimaryKeyConstraint('id')
    )
    # Create indexes for performance
    op.create_index(op.f('ix_prompt_template_type'), 'prompt_template', ['type'], unique=False)
    op.create_index(op.f('ix_prompt_template_group'), 'prompt_template', ['group'], unique=False)
    op.create_index(op.f('ix_prompt_template_order'), 'prompt_template', ['order'], unique=False)
    op.create_index(op.f('ix_prompt_template_type_group'), 'prompt_template', ['type', 'group'], unique=False)


def downgrade() -> None:
    """Downgrade schema."""
    # Drop prompt_template table and its indexes
    op.drop_index(op.f('ix_prompt_template_type_group'), table_name='prompt_template')
    op.drop_index(op.f('ix_prompt_template_order'), table_name='prompt_template')
    op.drop_index(op.f('ix_prompt_template_group'), table_name='prompt_template')
    op.drop_index(op.f('ix_prompt_template_type'), table_name='prompt_template')
    op.drop_table('prompt_template')
