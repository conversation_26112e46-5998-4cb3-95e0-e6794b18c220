from typing import Dict, Any, AsyncIterator, List
from agent_server.core.base.langchain_sender import LangchainBaseSender
from fastapi import Request
from agent_server.core.config.app_logger import logger
import traceback
from langchain.prompts import ChatPromptTemplate
from langchain_core.runnables import Runnable
from langchain_core.messages import AIMessageChunk
from agent_server.core.langchain.chain_config import ChainConfig
from abc import ABC, abstractmethod

# LangChain 顺序链基类，用于封装流式响应生成逻辑。 
# <AUTHOR> xiangjh

PROMPT_TEMPLATES = {
    "prompt1": """请介绍一下{city}的地理位置和人文特色。""",
    "prompt2": """{introduce} \n\n请对以上介绍行简单总结，并已诗歌形式输出，字数不超过20字""",
    "prompt3": """{poem} \n\n请分析这首诗的意境和风格""",
    "prompt4": """{analysis} \n\n请用一句话总结"""
}

# PROMPT_TEMPLATES = {
#     "prompt1": """请根据用户输入的{city}的城市名，选择调用本地的 get_myweather 工具查询{city}的天气情况。然后根据工具返回的结果生成一个json格式的天气信息。""",
#     "prompt2": """{weather} \n\n请根据查询的天气情况，如果为晴天，请输出“今天天气晴朗，适合户外活动，请做好防晒措施。”，否则输出 天气很糟糕！""",
# }
class SequentialChainSender(LangchainBaseSender, ABC):
    """
    支持动态链配置的流式LangChain发送器
    特性：
    - 支持多链顺序执行，前序链输出可作为后续链输入
    - 支持流式响应生成和中间结果处理
    - 提供灵活的提示词模板管理
    - 支持通过ChainConfig进行链式流程配置

    作者: xiangjh

    使用示例：
      class CustomSender(SequentialChainSender):
          def _def_chain_configs(self, extend_params):
              return [{
                  "chain_code": "chain1",
                  "prompt_name": "prompt1",
                  "input_key": "city",
                  "package_type": AppConstant.DEFAULT_PACKAGE_TYPE
              }]
    
    """

    @abstractmethod
    def _def_chain_configs(self, extend_params: dict) -> List[dict]:
        """
        抽象方法：实现链配置获取逻辑
        参数:
            extend_params: 扩展参数字典，可用于动态调整配置
            
        返回:
            List[dict]: 链式配置字典列表，每个字典应包含：
                - chain_code: 链的唯一标识
                - prompt_name: 使用的提示词模板名称
                - input_key: 输入参数的键名
                - package_type: 数据包类型（参考AppConstant）
                - is_intermediate: 是否中间结果（可选）
                - output_key: 输出结果的键名（可选）
        
        """
        pass

    def _parse_chain_configs(self, custom_configs: dict) -> List[ChainConfig]:
        """
        解析链式配置
        
        参数:
            custom_configs: 自定义配置字典
            
        返回:
            List[ChainConfig]: 转换后的链式配置对象列表
            
        说明:
            - 将原始字典配置转换为强类型的ChainConfig对象
            - 处理默认值（如is_intermediate默认为False）
        """
        if custom_configs:
            return [
                ChainConfig(
                    chain_code=cfg["chain_code"],
                    prompt_name=cfg["prompt_name"],
                    input_key=cfg["input_key"],
                    package_type=cfg["package_type"],
                    is_intermediate=cfg.get("is_intermediate", False),
                    output_key=cfg.get("output_key")
                ) for cfg in custom_configs
            ]
    
    async def _generate_content(self, 
                            request: Request,
                            question: str,
                            user_id: str,
                            conversation_id: str,
                            extend_params: dict) -> AsyncIterator[Any]:
        """
        生成流式内容的主方法
        
        参数:
            request: FastAPI请求对象
            question: 用户输入的问题
            user_id: 用户ID
            conversation_id: 会话ID
            extend_params: 扩展参数字典
            
        返回:
            AsyncIterator[Any]: 异步数据包生成器
            
        异常处理:
            - 捕获所有异常并包装为错误数据包
            - 记录详细错误日志
        """
        try:
            logger.info(f"开始执行链：{self.agent_code}")
            llm_client = await self._get_llm_client(request, user_id)
            
            # 获取链配置（允许从扩展参数覆盖默认配置）
            chain_configs = self._parse_chain_configs(self._def_chain_configs(extend_params))
            
            # 创建链对象
            chains = await self._create_chains(llm_client, chain_configs)
            
            # 执行链
            async for package in self._execute_chains(chains, chain_configs, question):
                yield package

        except Exception as e:
            error_msg = f"执行异常: {str(e)}"
            logger.error(error_msg, exc_info=True)
            traceback.print_exc()
            yield self._wrap_package(error_msg)

    

    async def _create_chains(self, llm_client, chain_configs: List[ChainConfig]) -> Dict[str, Runnable]:
        """创建所有链的Runnable对象"""
        chains = {}
        for config in chain_configs:
            chains[config.chain_code] = await self._create_chain_step(config.prompt_name, llm_client.model)
        return chains

    async def _create_chain_step(self, prompt_name: str, model) -> Runnable:
        """创建单个链步骤"""
        prompt = await self._get_prompt_template(prompt_name)
        return prompt | model
    
    async def _execute_chains(self, 
                             chains: Dict[str, Runnable], 
                             chain_configs: List[ChainConfig],
                             initial_input: str) -> AsyncIterator[Dict]:
        """
        执行链序列并处理流式输出
        执行链式流程
        
        参数:
            chains: 可运行链的字典
            chain_configs: 链式配置列表
            initial_input: 初始输入字符串
            
        返回:
            AsyncIterator[Dict]: 异步数据流生成器
            
        流程:
            1. 初始化输入数据
            2. 遍历执行每个链配置
            3. 发送开始标记数据包
            4. 流式处理链输出
            5. 保存中间结果
            6. 发送结束标记数据包
        """
        input_data = {chain_configs[0].input_key: initial_input}
        output_data = {}
        
        for config in chain_configs:
            chain = chains[config.chain_code]
            logger.info(f"开始执行链: {config.chain_code}, 输入: {input_data}")
            
            chain_output = []
            yield self._wrap_package(
                        "",
                        package_type=config.package_type,
                        is_last=False,
                        is_new_package=True
                        
            )
            async for chunk in chain.astream(input_data):
                content = self._extract_content(chunk)
                chain_output.append(content)
                yield self._wrap_package(
                        content,
                        config.package_type
                    )
                    
            
            full_output = "".join(chain_output)
            logger.debug(f"链 {config.chain_code} 完整输出: {full_output}")
            
            # 保存输出数据（用于后续链的输入）
            if config.output_key:
                output_data[config.output_key] = full_output
            
            # 如果不是中间结果，发送最终输出
            """ if not config.is_intermediate:
                # 流式发送完整输出（模拟token-by-token）
                for char in full_output:
                    yield self._wrap_package(char, config.package_type) """
            
            input_data = {config.output_key: full_output}
            input_data.update(output_data)
            yield self._wrap_package(
                "", 
                package_type=config.package_type,
                is_last=True,
                is_new_package=False
            )

    def _extract_content(self, output) -> str:
        """通用内容提取方法（增强版）
        
        支持类型:
            - AIMessageChunk: 提取content字段
            - 包含content属性的对象
            - 字典类型: 提取text字段
            - 字符串直接返回
            - 其他类型转为字符串
            
        返回:
            str: 提取后的文本内容
        """
        if isinstance(output, AIMessageChunk):
            return output.content
        if hasattr(output, 'content'):
            return output.content
        if isinstance(output, dict):
            return output.get('text', '')
        if isinstance(output, str):
            return output
        return str(output)
    
    async def _get_prompt_template(self, prompt_name: str) -> ChatPromptTemplate:
        """获取提示词模板"""
        template = PROMPT_TEMPLATES.get(prompt_name)
        if not template:
            raise ValueError(f"未找到名称为 {prompt_name} 的提示词模板")
        
        return ChatPromptTemplate.from_messages([
            ("system", "你是一个专业助手"),
            ("human", template)
        ])
    
    def _wrap_package(self, data: str, package_type: int = 0, 
                     is_last: bool = False, is_new_package: bool = False) -> Dict:
        """构建符合要求的数据包"""
        return {
            "data": data,
            "is_last": is_last,
            "is_new_package": is_new_package,
            "package_type": package_type
        }