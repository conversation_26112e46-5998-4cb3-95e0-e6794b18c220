
from agent_server.core.message.types import MessagePackage, MessageType
from agent_server.core.services.database.schemas.message import MessageCreate


def create_human_message(message: str, conversation_id: str):
    
    message_pkg = MessagePackage(
        package_id=0,
        package_type=0,
        status=1,
        data=message,
    )

    return MessageCreate(
        agent_code="default",
        conversation_id=conversation_id,
        message_type=MessageType.HUMAN.value,
        content=[message_pkg.model_dump()],
    )
