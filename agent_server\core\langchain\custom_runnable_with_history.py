from langchain_core.runnables.history import RunnableWithMessageHistory
from langchain_core.messages import BaseMessage
from typing import List, Dict, Any, Callable

# 自定义RunnableWithMessageHistory，支持自定义历史消息剪裁
# <AUTHOR> xiangjh
class RunnableWithCustomHistory(RunnableWithMessageHistory):
    """支持自定义历史消息剪裁的Runnable"""

    def __init__(
        self,
        runnable,
        get_session_history,
        input_messages_key="input",
        history_messages_key="chat_history",
        history_messages_trimmer=None,
        max_messages=4
    ):
        # 设置默认剪裁器
        def default_trimmer(messages: List[BaseMessage]) -> List[BaseMessage]:
            #print(f"【DEBUG】默认剪裁器被调用！原始消息数: {len(messages)}")
            return messages[-max_messages:]

        trimmer = default_trimmer  
        super().__init__(
            runnable=runnable,
            get_session_history=get_session_history,
            input_messages_key=input_messages_key,
            history_messages_key=history_messages_key,
            history_messages_trimmer=trimmer  #父类会处理剪裁器
        )

        #将 trimmer 赋值给实例方法，通过绑定函数绕过 Pydantic 校验
        self._custom_trimmer = trimmer

    #  覆盖父类方法，确保使用自定义剪裁逻辑
    def _get_trimmed_history(self, session_id: str) -> List[BaseMessage]:
        message_history = self.get_session_history(session_id)
        return self._custom_trimmer(message_history.messages)  # 使用自定义剪裁器

    def invoke(self, input: Dict[str, Any], config: Dict[str, Any] = None) -> Any:
        if not config or "session_id" not in config.get("configurable", {}):
            raise ValueError("session_id 是必需的参数")

        session_id = config["configurable"]["session_id"]
        trimmed_history = self._get_trimmed_history(session_id)

        runnable_input = {
            self.input_messages_key: input[self.input_messages_key],
            self.history_messages_key: trimmed_history,
        }

        return super().invoke(runnable_input, config)

    async def ainvoke(self, input: Dict[str, Any], config: Dict[str, Any] = None) -> Any:
        if not config or "session_id" not in config.get("configurable", {}):
            raise ValueError("session_id 是必需的参数")

        session_id = config["configurable"]["session_id"]
        trimmed_history = self._get_trimmed_history(session_id)
        print(f"ainvoke {session_id} 的历史消息数: {len(trimmed_history)}")

        runnable_input = {
            self.input_messages_key: input[self.input_messages_key],
            self.history_messages_key: trimmed_history,
        }

        return await super().ainvoke(runnable_input, config)