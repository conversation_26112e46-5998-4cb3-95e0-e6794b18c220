#!/usr/bin/env python3
"""
Build and publish script for agent-server package
"""

import os
import sys
import subprocess
import shutil
from pathlib import Path

def run_command(cmd, check=True):
    """Run a shell command"""
    print(f"Running: {cmd}")
    result = subprocess.run(cmd, shell=True, check=check)
    return result.returncode == 0

def clean_build():
    """Clean build artifacts"""
    print("Cleaning build artifacts...")
    
    dirs_to_clean = ["build", "dist", "*.egg-info"]
    for pattern in dirs_to_clean:
        for path in Path(".").glob(pattern):
            if path.is_dir():
                shutil.rmtree(path)
                print(f"Removed directory: {path}")
            elif path.is_file():
                path.unlink()
                print(f"Removed file: {path}")

def build_package():
    """Build the package"""
    print("Building package...")
    
    # Build using modern build tool
    if not run_command("python -m build"):
        print("Build failed!")
        return False
    
    print("Package built successfully!")
    return True

def check_package():
    """Check the built package"""
    print("Checking package...")
    
    # Check with twine
    if not run_command("python -m twine check dist/*"):
        print("Package check failed!")
        return False
    
    print("Package check passed!")
    return True

def publish_to_pypi(repository="pypi"):
    """Publish to PyPI or test PyPI"""
    print(f"Publishing to {repository}...")
    
    if repository == "testpypi":
        cmd = "python -m twine upload --repository testpypi dist/*"
    else:
        cmd = "python -m twine upload dist/*"
    
    if not run_command(cmd):
        print("Upload failed!")
        return False
    
    print(f"Package published to {repository} successfully!")
    return True

def publish_to_private_pypi(repository_url, username=None, password=None):
    """Publish to private PyPI server"""
    print(f"Publishing to private repository: {repository_url}")
    
    cmd = f"python -m twine upload --repository-url {repository_url}"
    
    if username:
        cmd += f" --username {username}"
    if password:
        cmd += f" --password {password}"
    
    cmd += " dist/*"
    
    if not run_command(cmd):
        print("Upload to private repository failed!")
        return False
    
    print("Package published to private repository successfully!")
    return True

def check_pypirc():
    """检查 .pypirc 配置文件"""
    from pathlib import Path

    home_dir = Path.home()
    pypirc_path = home_dir / ".pypirc"

    if not pypirc_path.exists():
        print("⚠️  未找到 .pypirc 配置文件")
        print("请运行: python scripts/setup_pypi_config.py --setup")
        return False

    # 检查是否包含模板占位符
    try:
        with open(pypirc_path, 'r') as f:
            content = f.read()

        if "your-api-token-here" in content:
            print("⚠️  .pypirc 文件包含模板占位符，请填入真实的 API Token")
            print("编辑文件: vim ~/.pypirc")
            return False

        print("✅ .pypirc 配置文件检查通过")
        return True

    except Exception as e:
        print(f"❌ 检查 .pypirc 文件失败: {e}")
        return False


def main():
    """Main function"""
    import argparse

    parser = argparse.ArgumentParser(description="Build and publish agent-server package")
    parser.add_argument("--clean", action="store_true", help="Clean build artifacts")
    parser.add_argument("--build", action="store_true", help="Build package")
    parser.add_argument("--check", action="store_true", help="Check package")
    parser.add_argument("--publish", choices=["pypi", "testpypi"], help="Publish to PyPI")
    parser.add_argument("--private-repo", help="Private repository URL")
    parser.add_argument("--username", help="Username for private repository")
    parser.add_argument("--password", help="Password for private repository")
    parser.add_argument("--all", action="store_true", help="Clean, build, check, and publish")
    parser.add_argument("--setup-config", action="store_true", help="Setup .pypirc configuration")

    args = parser.parse_args()

    if not any([args.clean, args.build, args.check, args.publish, args.private_repo, args.all, args.setup_config]):
        parser.print_help()
        return

    # 设置配置
    if args.setup_config:
        from setup_pypi_config import setup_pypirc
        setup_pypirc()
        return
    
    # Change to project root directory
    script_dir = Path(__file__).parent
    project_root = script_dir.parent
    os.chdir(project_root)
    
    success = True

    # 发布前检查配置
    if args.publish or args.private_repo:
        if not check_pypirc():
            print("❌ 配置检查失败，无法发布")
            return

    if args.all or args.clean:
        clean_build()

    if args.all or args.build:
        success = success and build_package()
    
    if success and (args.all or args.check):
        success = success and check_package()
    
    if success and args.publish:
        success = success and publish_to_pypi(args.publish)
    
    if success and args.private_repo:
        success = success and publish_to_private_pypi(
            args.private_repo, 
            args.username, 
            args.password
        )
    
    if success:
        print("All operations completed successfully!")
    else:
        print("Some operations failed!")
        sys.exit(1)

if __name__ == "__main__":
    main()
