# LangGraph 实现说明

## 概述

本目录实现了基于 LangGraph 的 Agent 架构

## 设计特点

### 1. 保持原有架构思路不变
- 通过 `agent_factory` 注册 agent
- 支持灵活切换 LLM 模型
- 支持不同节点动态加载不同的 prompt

### 2. 利用 LangGraph 的优势
- 使用图结构管理复杂状态和循环
- 支持分支和条件执行
- 更好的状态管理和持久化
- 支持复杂的工作流编排

## 核心组件

### 1. agent_builder.py
实现了基于 LangGraph 的 Agent 构建逻辑：
- 定义了 AgentState 状态结构
- 实现了构建 LangGraph Agent 的函数
- 支持动态加载不同 Agent 的提示词模板
- 支持工具节点的创建和集成

### 2. langgraph_sender.py
实现了基于 LangGraph 的 Sender：
- 继承自 BaseSender 抽象基类
- 实现了 `_generate_content` 方法用于生成流式响应
- 集成了 LangGraph Agent 的执行逻辑
- 通过 llm_client 获取已注册的工具

## 使用方法

### 1. 配置 Agent
在数据库或配置文件中创建 Agent 配置，将 `target_type` 设置为 `LANGGRAPH`。

### 2. 实现自定义节点（可选）
如果需要自定义 Agent 行为，可以继承 `LangGraphBaseSender` 并重写相关方法。

### 3. 动态 Prompt 加载
通过 `get_prompt_template_by_agent_id` 函数支持为不同 Agent 动态加载不同的提示词模板。

## 工具注册机制

LangGraph 实现遵循与 LangChain 相同的工具注册机制：

1. 工具通过 `llm_client` 初始化时调用 `_init_tools` 方法注册
2. LangGraph 在构建工具节点时，直接从 `llm_client` 获取已注册的工具
3. 这确保了工具注册的一致性和可维护性

## Agent类型支持

为了支持多种Agent实现方式，我们在 LLMClient 中增加了 `agent_type` 参数：

1. 当 `agent_type="langchain"` 时，初始化 LangChain 相关组件
2. 当 `agent_type="langgraph"` 时，初始化 LangGraph 相关组件
3. 这种设计避免了在一种Agent类型中初始化另一种Agent类型特有的组件

在 LangGraphBaseSender 中，我们明确指定 `agent_type="langgraph"`，这样就不会初始化 LangChain 特有的 [_init_langchain_agent](file://d:\projects\agent-server\llmclient\llm_client.py#L178-L210) 方法。


## 未来优化方向

1. 增加更多节点类型支持
2. 实现更复杂的条件边逻辑
3. 增加对持久化检查点的支持
4. 实现图的可视化功能