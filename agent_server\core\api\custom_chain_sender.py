from agent_server.core.base.langchain_sequential_sender import <PERSON><PERSON><PERSON>hain<PERSON><PERSON>
from typing import List
from agent_server.core.config.app_constant import AppConstant


class CustomChainSender(SequentialChainSender):
    

    def _def_chain_configs(self, extend_params: dict) -> List[dict]:
        custom_configs = extend_params.get("chain_configs")
        custom_configs = [
            {"chain_code": "chain1", "prompt_name": "prompt1", "input_key": "city", "package_type": AppConstant.DEFAULT_PACKAGE_TYPE, "is_intermediate": True, "output_key": "introduce"},
            {"chain_code": "chain2", "prompt_name": "prompt2", "input_key": "introduce", "package_type": AppConstant.DEFAULT_PACKAGE_TYPE, "output_key": "poem"},
            {"chain_code": "chain3", "prompt_name": "prompt3", "input_key": "poem", "package_type": AppConstant.PACKAGE_TYPE_THINKING, "output_key": "analysis"},
            {"chain_code": "chain4", "prompt_name": "prompt4", "input_key": "analysis", "package_type": AppConstant.DEFAULT_PACKAGE_TYPE, "output_key": "last_output"}
        ]
        return custom_configs
        