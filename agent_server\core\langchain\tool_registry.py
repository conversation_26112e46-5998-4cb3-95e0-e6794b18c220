
from langchain_core.tools import tool
from typing import List, Dict, Any
import logging

# 工具注册器，将MCP的工具注册到LangChain中
# <AUTHOR> xiangjh

logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)
def register_tools(mcp_client, tools_response, exclude_tools: list = None):
    """MCP 工具注册引擎
    
    实现 MCP 服务工具到 LangChain 工具系统的自动化注册，支持：
    - 动态工具发现与注册
    - 工具元数据解析
    - 执行结果标准化处理
    - 工具排除机制

    Args:
        mcp_client (McpClient): MCP 服务客户端实例，需实现 execute_tool 方法
        tools_response (ToolResponse): MCP 服务返回的工具列表响应对象
        exclude_tools (list, optional): 需要排除的工具名称列表，默认为空

    Returns:
        list: 已注册的 LangChain Tool 对象列表，可直接用于 Agent 构建

    Raises:
        ConnectionError: 当 MCP 客户端连接异常时抛出
        ValueError: 当工具元数据不完整时抛出

    Example:
        >>> from agent_server.mcpclient import McpClient
        >>> client = McpClient()
        >>> tools = register_tools(client, mcp_service.list_tools())
        >>> len(tools) > 0
        True
    """
    tools = []
    exclude_tools = exclude_tools or []  

    for cust_tool in tools_response.tools:
        tool_name = cust_tool.name
        tool_description = cust_tool.description
        tool_arg_schema = cust_tool.inputSchema

        if tool_name in exclude_tools:
            logger.info(f"跳过注册工具: {tool_name}")
            continue

        def make_tool_func(name: str, desc: str, args_schema: Dict[str, Any]):
            @tool(name, args_schema=args_schema)
            async def tool_func(**kwargs):
                """{description}"""
                result = await mcp_client.execute_tool(name, kwargs)
                return result.content

            tool_func.__doc__ = desc
            tool_func.description = desc
            return tool_func

        tool_func = make_tool_func(tool_name, tool_description, tool_arg_schema)
        tools.append(tool_func)
    return tools