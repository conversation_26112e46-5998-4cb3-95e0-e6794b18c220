"""change field file type  length to 100

Revision ID: 4ad8811cb3f9
Revises: eac65d3a9464
Create Date: 2025-09-16 17:46:28.989456

"""
from typing import Sequence, Union

from alembic import op
import sqlalchemy as sa
from sqlalchemy.dialects import postgresql
import sqlmodel

# revision identifiers, used by Alembic.
revision: str = '4ad8811cb3f9'
down_revision: Union[str, None] = 'eac65d3a9464'
branch_labels: Union[str, Sequence[str], None] = None
depends_on: Union[str, Sequence[str], None] = None


def upgrade() -> None:
    """Upgrade schema."""
    # ### commands auto generated by Alembic - please adjust! ###
    op.drop_table('ai_document_chunk')
    op.drop_table('ai_document')
    op.drop_table('ai_knowledge_base')
    op.alter_column('user_uploaded_files', 'rag_server',
               existing_type=sa.VARCHAR(length=50),
               type_=sqlmodel.sql.sqltypes.AutoString(length=100),
               existing_nullable=False,
               existing_server_default=sa.text("'ragflow'::character varying"))
    # ### end Alembic commands ###


def downgrade() -> None:
    """Downgrade schema."""
    # ### commands auto generated by Alembic - please adjust! ###
    op.alter_column('user_uploaded_files', 'rag_server',
               existing_type=sqlmodel.sql.sqltypes.AutoString(length=100),
               type_=sa.VARCHAR(length=50),
               existing_nullable=False,
               existing_server_default=sa.text("'ragflow'::character varying"))
    op.create_table('ai_knowledge_base',
    sa.Column('name', sa.VARCHAR(), autoincrement=False, nullable=False),
    sa.Column('description', sa.VARCHAR(), autoincrement=False, nullable=True),
    sa.Column('collection_name', sa.VARCHAR(), autoincrement=False, nullable=False),
    sa.Column('embedding_model', sa.VARCHAR(), autoincrement=False, nullable=False),
    sa.Column('dimension', sa.INTEGER(), autoincrement=False, nullable=False),
    sa.Column('chunk_size', sa.INTEGER(), autoincrement=False, nullable=False),
    sa.Column('chunk_overlap', sa.INTEGER(), autoincrement=False, nullable=False),
    sa.Column('separators', sa.VARCHAR(), autoincrement=False, nullable=False),
    sa.Column('document_count', sa.INTEGER(), autoincrement=False, nullable=False),
    sa.Column('chunk_count', sa.INTEGER(), autoincrement=False, nullable=False),
    sa.Column('metadata_schema', sa.VARCHAR(), autoincrement=False, nullable=True),
    sa.Column('status', sa.VARCHAR(), autoincrement=False, nullable=False),
    sa.Column('is_deleted', sa.BOOLEAN(), autoincrement=False, nullable=False),
    sa.Column('create_time', postgresql.TIMESTAMP(timezone=True), autoincrement=False, nullable=True),
    sa.Column('update_time', postgresql.TIMESTAMP(timezone=True), autoincrement=False, nullable=True),
    sa.Column('create_by', sa.VARCHAR(), autoincrement=False, nullable=False),
    sa.Column('update_by', sa.VARCHAR(), autoincrement=False, nullable=True),
    sa.Column('id', sa.INTEGER(), server_default=sa.text("nextval('ai_knowledge_base_id_seq'::regclass)"), autoincrement=True, nullable=False),
    sa.PrimaryKeyConstraint('id', name='ai_knowledge_base_pkey'),
    postgresql_ignore_search_path=False
    )
    op.create_table('ai_document',
    sa.Column('knowledge_base_id', sa.INTEGER(), autoincrement=False, nullable=False),
    sa.Column('title', sa.VARCHAR(), autoincrement=False, nullable=False),
    sa.Column('content', sa.VARCHAR(), autoincrement=False, nullable=True),
    sa.Column('file_name', sa.VARCHAR(), autoincrement=False, nullable=True),
    sa.Column('file_type', sa.VARCHAR(), autoincrement=False, nullable=True),
    sa.Column('file_size', sa.INTEGER(), autoincrement=False, nullable=True),
    sa.Column('status', sa.VARCHAR(), autoincrement=False, nullable=False),
    sa.Column('error_message', sa.VARCHAR(), autoincrement=False, nullable=True),
    sa.Column('chunk_count', sa.INTEGER(), autoincrement=False, nullable=False),
    sa.Column('character_count', sa.INTEGER(), autoincrement=False, nullable=False),
    sa.Column('chunk_size', sa.INTEGER(), autoincrement=False, nullable=True),
    sa.Column('chunk_overlap', sa.INTEGER(), autoincrement=False, nullable=True),
    sa.Column('separators', sa.VARCHAR(), autoincrement=False, nullable=True),
    sa.Column('doc_metadata', sa.VARCHAR(), autoincrement=False, nullable=True),
    sa.Column('is_deleted', sa.BOOLEAN(), autoincrement=False, nullable=False),
    sa.Column('create_time', postgresql.TIMESTAMP(timezone=True), autoincrement=False, nullable=True),
    sa.Column('update_time', postgresql.TIMESTAMP(timezone=True), autoincrement=False, nullable=True),
    sa.Column('processed_at', postgresql.TIMESTAMP(timezone=True), autoincrement=False, nullable=True),
    sa.Column('create_by', sa.VARCHAR(), autoincrement=False, nullable=False),
    sa.Column('update_by', sa.VARCHAR(), autoincrement=False, nullable=True),
    sa.Column('id', sa.INTEGER(), server_default=sa.text("nextval('ai_document_id_seq'::regclass)"), autoincrement=True, nullable=False),
    sa.ForeignKeyConstraint(['knowledge_base_id'], ['ai_knowledge_base.id'], name='ai_document_knowledge_base_id_fkey'),
    sa.PrimaryKeyConstraint('id', name='ai_document_pkey'),
    postgresql_ignore_search_path=False
    )
    op.create_table('ai_document_chunk',
    sa.Column('document_id', sa.INTEGER(), autoincrement=False, nullable=False),
    sa.Column('knowledge_base_id', sa.INTEGER(), autoincrement=False, nullable=False),
    sa.Column('text', sa.VARCHAR(), autoincrement=False, nullable=False),
    sa.Column('chunk_index', sa.INTEGER(), autoincrement=False, nullable=False),
    sa.Column('start_index', sa.INTEGER(), autoincrement=False, nullable=False),
    sa.Column('end_index', sa.INTEGER(), autoincrement=False, nullable=False),
    sa.Column('vector_id', sa.VARCHAR(), autoincrement=False, nullable=True),
    sa.Column('embedding_model', sa.VARCHAR(), autoincrement=False, nullable=True),
    sa.Column('character_count', sa.INTEGER(), autoincrement=False, nullable=False),
    sa.Column('recall_count', sa.INTEGER(), autoincrement=False, nullable=False),
    sa.Column('chunk_metadata', sa.VARCHAR(), autoincrement=False, nullable=True),
    sa.Column('keywords', sa.VARCHAR(), autoincrement=False, nullable=True),
    sa.Column('is_deleted', sa.BOOLEAN(), autoincrement=False, nullable=False),
    sa.Column('create_time', postgresql.TIMESTAMP(timezone=True), autoincrement=False, nullable=True),
    sa.Column('update_time', postgresql.TIMESTAMP(timezone=True), autoincrement=False, nullable=True),
    sa.Column('last_recalled_at', postgresql.TIMESTAMP(timezone=True), autoincrement=False, nullable=True),
    sa.Column('create_by', sa.VARCHAR(), autoincrement=False, nullable=False),
    sa.Column('update_by', sa.VARCHAR(), autoincrement=False, nullable=True),
    sa.Column('id', sa.INTEGER(), autoincrement=True, nullable=False),
    sa.ForeignKeyConstraint(['document_id'], ['ai_document.id'], name='ai_document_chunk_document_id_fkey'),
    sa.ForeignKeyConstraint(['knowledge_base_id'], ['ai_knowledge_base.id'], name='ai_document_chunk_knowledge_base_id_fkey'),
    sa.PrimaryKeyConstraint('id', name='ai_document_chunk_pkey')
    )
    # ### end Alembic commands ###
