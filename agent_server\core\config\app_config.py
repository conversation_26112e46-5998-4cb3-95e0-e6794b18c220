import os
import yaml
from pathlib import Path
from typing import Optional, Dict, Any, List
from pydantic import BaseModel, Field, model_validator
from agent_server.core.config.app_logger import logger


# 加载系统配置信息，环境变量
# <AUTHOR> xiangjh
class RedisConfig(BaseModel):
    host: str = "127.0.0.1"
    port: int = 6379
    password: Optional[str] = None
    db: int = 0
    decode_responses: bool = True
    url: Optional[str] = None

    @model_validator(mode="after")
    def validate_password(self):
        if self.password is None:
            raw_pass = os.getenv("REDIS_PASSWORD")
            if raw_pass:
                self.password = raw_pass
        return self


class PostgreSQLConfig(BaseModel):
    url: str = "postgresql+asyncpg://postgres:postgres@localhost:5432/agent_db"


class ModelConfig(BaseModel):
    name: str
    model_key: str
    api_key: str
    base_url: str
    temperature: float = 0.0


class SystemConfig(BaseModel):
    host: str
    port: int = 8000


class AgentConfig(BaseModel):
    name: str
    description: Optional[str] = None
    target: Optional[str] = None
    base_url: Optional[str] = None
    api_key: Optional[str] = None
    impl: Optional[str] = None
    exclude_tools: List[str] = Field(default_factory=list)
    checkpoint: str = "memory"  # 默认值为"memory" ，可选值为"redis"


class McpServer(BaseModel):
    key: str
    type: str
    url: str
    enabled: bool


class AuthConfig(BaseModel):
    adapter: str = "core.auth.default_auth_adapter.DefaultAuthAdapter"
    auth_url: str


class AdminUserConfig(BaseModel):
    password: str
    role: str = "admin"


class AdminSystemConfig(BaseModel):
    title: str = "Agent Server Management"
    session_timeout: int = 3600
    port: int = 8001
    storage_secret: Optional[str] = None


class AdminConfig(BaseModel):
    users: Dict[str, AdminUserConfig] = Field(default_factory=dict)
    system: AdminSystemConfig = Field(default_factory=AdminSystemConfig)


class MessageConfig(BaseModel):
    task_expired: int = 86400  # 任务过期时间(秒) - 24小时
    waiting_timeout: int = 60  # 等待超时时间(秒)

class RAGServiceConfig(BaseModel):
    services: List[Any]

class AppConfig(BaseModel):
    redis: RedisConfig
    model: ModelConfig
    model_list: List[ModelConfig] = Field(default_factory=list)
    full_config: Dict[str, Any] = Field(default_factory=dict)
    business_system: SystemConfig
    database: PostgreSQLConfig
    agents: List[AgentConfig] = []
    mcp_servers: List[McpServer] = []
    auth: AuthConfig
    admin: Optional[AdminConfig] = None
    message: MessageConfig = Field(default_factory=MessageConfig)
    rag_service: Optional[RAGServiceConfig] = None

    def get_model_by_name(self, model_name: str) -> Optional[ModelConfig]:
        """根据模型名称获取模型配置

        Args:
            model_name (str): 模型名称

        Returns:
            Optional[ModelConfig]: 找到的模型配置，如果未找到则返回 None
        """
        for model in self.model_list:
            if model.name == model_name:
                return model
        return None

    def get_model_by_key(self, model_key: str) -> Optional[ModelConfig]:
        """根据模型键获取模型配置

        Args:
            model_key (str): 模型键

        Returns:
            Optional[ModelConfig]: 找到的模型配置，如果未找到则返回 None
        """
        for model in self.model_list:
            if model.model_key == model_key:
                return model
        return None

    def get_all_model_names(self) -> List[str]:
        """获取所有可用的模型名称列表

        Returns:
            List[str]: 模型名称列表
        """
        return [model.name for model in self.model_list]


def load_config() -> AppConfig:
    # 从环境变量中获取配置文件路径
    config_path = os.getenv("CONFIG_PATH", "agent_server/config/application.yml")
    config_path = Path.joinpath(Path.cwd(), config_path)
    with open(config_path, "r", encoding="utf-8") as f:
        full_config = yaml.safe_load(f)

    # 加载 Redis 配置（优先级：环境变量 > yml）
    redis_config = full_config.get("redis", {})
    redis_data = {
        "host": redis_config.get("host", "127.0.0.1"),
        "port": int(redis_config.get("port", 6379)),
        "password": redis_config.get("password"),
        "db": int(redis_config.get("db", 0)),
        "decode_responses": redis_config.get("decode_responses", True),
    }
    # 自动生成 Redis URL
    password_part = f":{redis_data['password']}@" if redis_data["password"] else ""
    redis_data["url"] = (
        f"redis://{password_part}{redis_data['host']}:{redis_data['port']}/{redis_data['db']}"
    )

    database_config = full_config.get("database", {})
    database_data = {"url": database_config.get("url")}

    # 加载 AI 模型配置
    ai_config = full_config.get("ai", {})
    models_config = ai_config.get("models", {})
    default_model_name = models_config.get("default")

    # 加载所有模型到 model_list
    model_list = []
    for model_key, model_config in models_config.items():
        if model_key != "default" and isinstance(model_config, dict):
            model_data = {
                "name": model_config.get("name"),
                "model_key": model_key,  # 使用配置文件中的键作为 model_key
                "api_key": model_config.get("api_key"),
                "base_url": model_config.get("base_url"),
                "temperature": float(model_config.get("temperature", 0.0)),
            }
            if model_data["name"]:  # 只添加有效的模型配置
                model_list.append(ModelConfig(**model_data))
                logger.info(
                    f"加载模型配置: {model_data['name']} (key: {model_key}) - {model_data['base_url']}"
                )

    logger.info(f"总共加载了 {len(model_list)} 个模型配置")

    # 加载默认模型配置（保持向后兼容）
    selected_model = models_config.get(default_model_name, {})
    default_model_data = {
        "name": selected_model.get("name"),
        "model_key": default_model_name,  # 使用默认模型名作为 model_key
        "api_key": selected_model.get("api_key"),
        "base_url": selected_model.get("base_url"),
        "temperature": float(selected_model.get("temperature", 0.0)),
    }

    if not default_model_data["name"]:
        raise ValueError("未找到有效的默认模型名称，请检查配置文件或环境变量")

    system_config = full_config.get("business_system", {})
    system_data = {
        "host": system_config.get("host"),
        "port": system_config.get("port"),
    }

    auth_config = full_config.get("auth", {})
    auth_data = {
        "adapter": auth_config.get("adapter"),
        "auth_url": auth_config.get("auth_url"),
    }

    # 加载管理后台配置
    admin_config_data = full_config.get("admin")
    admin_config = None
    if admin_config_data:
        try:
            users_data = {}
            for username, user_config in admin_config_data.get("users", {}).items():
                users_data[username] = AdminUserConfig(**user_config)

            system_config = admin_config_data.get("system", {})
            admin_system = AdminSystemConfig(**system_config)

            admin_config = AdminConfig(users=users_data, system=admin_system)
            logger.info(f"加载管理后台配置成功，用户数: {len(users_data)}")
        except Exception as e:
            logger.error(f"加载管理后台配置失败: {e}")

    # 加载消息配置
    message_config_data = full_config.get("message", {})
    message_config = MessageConfig(
        task_expired=message_config_data.get("task_expired", 86400),
        waiting_timeout=message_config_data.get("waiting_timeout", 60),
    )
    logger.info(
        f"加载消息配置成功: task_expired={message_config.task_expired}, waiting_timeout={message_config.waiting_timeout}"
    )

    # 加载RAG服务配置
    rag_service_data = full_config.get("rag_service", {"services": []})
    rag_service_config = RAGServiceConfig(services=rag_service_data.get("services", []))

    # 返回 Pydantic 类型安全的配置对象
    return AppConfig(
        redis=RedisConfig(**redis_data),
        database=PostgreSQLConfig(**database_data),
        model=ModelConfig(**default_model_data),
        model_list=model_list,
        business_system=SystemConfig(**system_data),
        full_config=full_config,
        agents=[AgentConfig(**agent) for agent in full_config.get("agents", [])],
        mcp_servers=[
            McpServer(**server) for server in full_config.get("mcp_servers", [])
        ],
        auth=AuthConfig(**auth_data),
        admin=admin_config,
        message=message_config,
        rag_service=rag_service_config,
    )


# 实例化全局配置
config = load_config()
