"""
Agent Server - A comprehensive AI agent framework

This package provides a complete framework for building AI agents with support for:
- <PERSON><PERSON><PERSON><PERSON> and LangGraph integration
- Multiple LLM providers
- MCP (Model Context Protocol) support
- Database integration with PostgreSQL
- Redis caching
- FastAPI web framework
"""

__version__ = "1.0.0"
__author__ = "Elephant"
__email__ = "<EMAIL>"

# Core imports for easy access
from agent_server.core.config.app_config import config, ModelConfig
from agent_server.core.factory.agent_factory import AgentFactory
from agent_server.core.services.model_config_service import ModelConfigService
from agent_server.llmclient.llm_client import LLMClient

# Base classes for extending
from agent_server.core.base.base_sender import Base<PERSON><PERSON>
from agent_server.core.base.langchain_sender import LangchainBaseSender
from agent_server.core.base.langgraph_sender import LangGraphBase<PERSON>ender
from agent_server.core.base.http_sender import HttpBaseSender

__all__ = [
    # Configuration
    "config",
    "ModelConfig",
    
    # Factories and Services
    "AgentFactory", 
    "ModelConfigService",
    
    # Clients
    "LLMClient",
    
    # Base Classes
    "BaseSender",
    "LangchainBaseSender", 
    "LangGraphBaseSender",
    "HttpBaseSender",
    
    # Metadata
    "__version__",
    "__author__",
    "__email__",
]
