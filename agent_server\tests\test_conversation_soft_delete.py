"""
测试会话软删除功能
"""
import unittest
import asyncio
from datetime import datetime, timezone

from agent_server.core.services.database.schemas.conversation import (
    ConversationTable,
    ConversationCreate,
    ConversationUpdate,
)
from agent_server.core.services.database.crud.conversation import conversation_curd
from agent_server.core.services.database.base import DatabaseManager


class TestConversationSoftDelete(unittest.TestCase):
    """会话软删除功能测试"""

    @classmethod
    def setUpClass(cls):
        """测试类级别的设置"""
        cls.loop = asyncio.new_event_loop()
        asyncio.set_event_loop(cls.loop)
        # 为测试创建独立的数据库管理器实例
        cls.db_manager = DatabaseManager()

    @classmethod
    def tearDownClass(cls):
        """测试类级别的清理"""
        # 关闭事件循环
        cls.loop.close()

    def setUp(self):
        """每个测试方法执行前的设置"""
        # 创建测试用的会话
        self.sample_conversation = self.loop.run_until_complete(self._create_sample_conversation())

    def tearDown(self):
        """每个测试方法执行后的清理"""
        # 清理：硬删除测试数据
        self.loop.run_until_complete(self._cleanup_sample_conversation())

    async def _create_sample_conversation(self):
        """创建测试用的会话"""
        async with self.db_manager.session() as session:
            conversation_data = ConversationCreate(
                title="Test Conversation",
                user_id=1,
                current_agent_code="test_agent",
                created_at=datetime.now(timezone.utc),
                updated_at=datetime.now(timezone.utc),
            )
            return await conversation_curd.create(db=session, obj_input=conversation_data)

    async def _cleanup_sample_conversation(self):
        """清理测试数据"""
        try:
            async with self.db_manager.session() as session:
                await conversation_curd.remove(db=session, _id=self.sample_conversation.id, hard_delete=True)
        except Exception:
            pass  # 如果已经被删除则忽略

    def test_soft_delete_conversation(self):
        """测试软删除会话"""
        self.loop.run_until_complete(self._test_soft_delete_conversation())

    async def _test_soft_delete_conversation(self):
        """测试软删除会话的异步实现"""
        async with self.db_manager.session() as session:
            # 确认会话存在
            conversation = await conversation_curd.get(db=session, _id=self.sample_conversation.id)
            self.assertIsNotNone(conversation)
            self.assertFalse(conversation.is_deleted)

            # 执行软删除
            deleted_conversation = await conversation_curd.soft_delete(db=session, _id=self.sample_conversation.id)
            self.assertIsNotNone(deleted_conversation)
            self.assertTrue(deleted_conversation.is_deleted)

            # 确认正常查询无法获取到已删除的会话
            conversation = await conversation_curd.get(db=session, _id=self.sample_conversation.id)
            self.assertIsNone(conversation)

            # 确认包含已删除记录的查询可以获取到会话
            conversation = await conversation_curd.get(db=session, _id=self.sample_conversation.id, include_deleted=True)
            self.assertIsNotNone(conversation)
            self.assertTrue(conversation.is_deleted)

    def test_restore_conversation(self):
        """测试恢复已删除的会话"""
        self.loop.run_until_complete(self._test_restore_conversation())

    async def _test_restore_conversation(self):
        """测试恢复已删除的会话的异步实现"""
        async with self.db_manager.session() as session:
            # 先软删除会话
            await conversation_curd.soft_delete(db=session, _id=self.sample_conversation.id)

            # 确认会话被删除
            conversation = await conversation_curd.get(db=session, _id=self.sample_conversation.id)
            self.assertIsNone(conversation)

            # 恢复会话
            restored_conversation = await conversation_curd.restore(db=session, _id=self.sample_conversation.id)
            self.assertIsNotNone(restored_conversation)
            self.assertFalse(restored_conversation.is_deleted)

            # 确认会话可以正常查询到
            conversation = await conversation_curd.get(db=session, _id=self.sample_conversation.id)
            self.assertIsNotNone(conversation)
            self.assertFalse(conversation.is_deleted)

    def test_get_by_user_id_excludes_deleted(self):
        """测试按用户ID查询会话时排除已删除的会话"""
        self.loop.run_until_complete(self._test_get_by_user_id_excludes_deleted())

    async def _test_get_by_user_id_excludes_deleted(self):
        """测试按用户ID查询会话时排除已删除的会话的异步实现"""
        async with self.db_manager.session() as session:
            # 确认会话在列表中
            conversations = await conversation_curd.get_by_user_id(db=session, user_id=1)
            conversation_ids = [c.id for c in conversations]
            self.assertIn(self.sample_conversation.id, conversation_ids)

            # 软删除会话
            await conversation_curd.soft_delete(db=session, _id=self.sample_conversation.id)

            # 确认会话不在列表中
            conversations = await conversation_curd.get_by_user_id(db=session, user_id=1)
            conversation_ids = [c.id for c in conversations]
            self.assertNotIn(self.sample_conversation.id, conversation_ids)

    def test_get_deleted_conversations(self):
        """测试获取已删除的会话列表"""
        self.loop.run_until_complete(self._test_get_deleted_conversations())

    async def _test_get_deleted_conversations(self):
        """测试获取已删除的会话列表的异步实现"""
        async with self.db_manager.session() as session:
            # 软删除会话
            await conversation_curd.soft_delete(db=session, _id=self.sample_conversation.id)

            # 获取已删除的会话列表
            deleted_conversations = await conversation_curd.get_deleted_conversations(db=session, user_id=1)
            deleted_ids = [c.id for c in deleted_conversations]
            self.assertIn(self.sample_conversation.id, deleted_ids)

            # 确认所有返回的会话都是已删除状态
            for conversation in deleted_conversations:
                self.assertTrue(conversation.is_deleted)

    def test_hard_delete_conversation(self):
        """测试硬删除会话"""
        self.loop.run_until_complete(self._test_hard_delete_conversation())

    async def _test_hard_delete_conversation(self):
        """测试硬删除会话的异步实现"""
        async with self.db_manager.session() as session:
            # 执行硬删除
            deleted_conversation = await conversation_curd.remove(db=session, _id=self.sample_conversation.id, hard_delete=True)
            self.assertIsNotNone(deleted_conversation)

            # 确认会话完全不存在（包括已删除记录查询）
            conversation = await conversation_curd.get(db=session, _id=self.sample_conversation.id, include_deleted=True)
            self.assertIsNone(conversation)

    def test_conversation_base_has_is_deleted_field(self):
        """测试ConversationTable模型包含is_deleted字段"""
        # 创建会话实例
        conversation = ConversationTable(
            title="Test",
            user_id=1,
            current_agent_code="test",
        )

        # 确认is_deleted字段存在且默认为False
        self.assertTrue(hasattr(conversation, 'is_deleted'))
        self.assertFalse(conversation.is_deleted)

    def test_conversation_update_supports_is_deleted(self):
        """测试ConversationUpdate模型支持is_deleted字段"""
        update_data = ConversationUpdate(is_deleted=True)
        self.assertTrue(hasattr(update_data, 'is_deleted'))
        self.assertTrue(update_data.is_deleted)


if __name__ == '__main__':
    unittest.main()
