from fastapi import APIRouter, Depends
from agent_server.core.config.app_logger import logger
from agent_server.core.auth.security import check_user
from agent_server.core.api.router_registry import (
    load_routers_from_config,
    apply_routers_to_app,
)

# 私有路由，所有请求都必须进行权限校验
router_private = APIRouter(dependencies=[Depends(check_user)], tags=["Private"])

# 公开路由，不进行权限校验
router_public = APIRouter(tags=["Public"])


# 注册核心路由（框架核心功能）
def register_core_routers():
    """注册核心路由模块"""
    try:
        from agent_server.core.api.login import router as login_router
        from agent_server.core.api.conversation import router as conversation_router
        from agent_server.core.api.message import router as message_router
        from agent_server.core.api.chat import router as chat_router
        from agent_server.core.api.agent import router as agent_router
        from agent_server.core.api.popular_question import (
            router as popular_question_router,
        )
        from agent_server.core.api.prompt_template import (
            router as prompt_template_router,
            user_router as user_prompt_template_router,
        )
        from agent_server.core.api.recommend_question import (
            router as recommend_question_router,
        )
        from agent_server.core.api.log_config import router as log_config_router
        from agent_server.core.api.rag import router as rag_router

        # 注册私有路由
        router_private.include_router(conversation_router)
        router_private.include_router(message_router)
        router_private.include_router(chat_router)
        router_private.include_router(popular_question_router)
        router_private.include_router(prompt_template_router)
        router_private.include_router(user_prompt_template_router)
        router_private.include_router(recommend_question_router)
        router_private.include_router(log_config_router)
        router_private.include_router(agent_router)
        router_private.include_router(rag_router)

        # 注册公开路由
        router_public.include_router(login_router)

        logger.info("核心路由模块注册完成")

    except Exception as e:
        logger.error(f"注册核心路由时出错: {str(e)}")


def initialize_routers():
    """初始化所有路由"""
    try:
        # 1. 注册核心路由
        register_core_routers()

        # 2. 从配置文件加载路由注册信息
        load_routers_from_config()

        # 3. 应用动态注册的路由
        apply_routers_to_app(router_private, router_public)

        logger.info("所有路由加载完成！")

    except Exception as e:
        logger.error(f"初始化路由时出错: {str(e)}")

# 初始化路由系统
initialize_routers()