from typing import List, Optional
from sqlalchemy.ext.asyncio import AsyncSession
from sqlmodel import Session, select
from sqlalchemy.orm.scoping import ScopedSession
import sqlalchemy as sa

from agent_server.core.services.database.crud.base import CRUDBase
from agent_server.core.services.database.schemas.message import MessageTable
from agent_server.core.services.database.schemas.message import (
    MessageCreate,
    MessageUpdate,
)


class CRUDMessage(CRUDBase[MessageTable, MessageCreate, MessageUpdate]):
    """消息CRUD操作实现"""

    async def get_by_user_id(
            self, db: AsyncSession, *, user_id: int, skip: int = 0, limit: int = 100
    ) -> List[MessageTable]:
        """根据用户ID获取消息列表"""
        query = (
            select(MessageTable)
            .where(MessageTable.user_id == user_id)
            .offset(skip)
            .limit(limit)
        )
        result = await db.execute(query)
        return result.scalars().all()

    async def get_by_conversation_id(
            self, db: AsyncSession, *, conversation_id: int, skip: int = 0
    ) -> List[MessageTable]:
        """根据对话ID获取消息列表 按照创建时间降序排序"""

        query = (
            select(MessageTable)
            .where(MessageTable.conversation_id == conversation_id)
            .offset(skip)
            .order_by(MessageTable.created_at)
        )
        result = await db.execute(query)
        return result.scalars().all()

    async def get_by_session_id(
            self, db: AsyncSession, *, session_id: str
    ) -> List[MessageTable]:
        """根据会话ID获取消息"""
        query = (
            select(MessageTable)
            .where(MessageTable.session_id == session_id)
            .order_by(MessageTable.created_at)
        )
        result = await db.execute(query)
        return result.scalars().all()

    async def get_by_message_type(
            self, db: AsyncSession, *, message_type: str, skip: int = 0, limit: int = 100
    ) -> List[MessageTable]:
        """根据消息类型获取消息列表"""
        query = (
            select(MessageTable)
            .where(MessageTable.message_type == message_type)
            .offset(skip)
            .limit(limit)
        )
        result = await db.execute(query)
        return result.scalars().all()

    async def rate_message(
            self, db: AsyncSession, *, message_id: str, rating: str, user_id: Optional[int] = None
    ) -> Optional[MessageTable]:
        """对消息进行评价

        Args:
            db: 数据库会话
            message_id: 消息ID
            rating: 评价类型 ("like" 或 "dislike")
            user_id: 用户ID (可选，用于后续扩展用户评价记录)

        Returns:
            更新后的消息对象，如果消息不存在则返回None
        """
        # 获取消息
        message = await self.get(db, _id=message_id)
        if not message:
            return None

        # 根据评价类型更新计数
        if rating == "like":
            # 如果之前是反对，需要减少反对数
            if message.user_rating == "dislike":
                message.dislike_count = max(0, message.dislike_count - 1)
            # 如果之前不是同意，增加同意数
            if message.user_rating != "like":
                message.like_count += 1
        elif rating == "dislike":
            # 如果之前是同意，需要减少同意数
            if message.user_rating == "like":
                message.like_count = max(0, message.like_count - 1)
            # 如果之前不是反对，增加反对数
            if message.user_rating != "dislike":
                message.dislike_count += 1
        elif rating is None or rating == "":
            # 取消评价
            if message.user_rating == "like":
                message.like_count = max(0, message.like_count - 1)
            elif message.user_rating == "dislike":
                message.dislike_count = max(0, message.dislike_count - 1)

        # 更新用户评价状态
        message.user_rating = rating if rating in ["like", "dislike"] else None

        # 保存到数据库
        db.add(message)
        await db.commit()
        await db.refresh(message)

        return message

    async def get_message_rating(
            self, db: AsyncSession, *, message_id: str
    ) -> Optional[dict]:
        """获取消息评价信息

        Args:
            db: 数据库会话
            message_id: 消息ID

        Returns:
            包含评价信息的字典，如果消息不存在则返回None
        """
        message = await self.get(db, _id=message_id)
        if not message:
            return None

        return {
            "message_id": message_id,
            "like_count": message.like_count,
            "dislike_count": message.dislike_count,
            "user_rating": message.user_rating,
        }

    async def get_latest_valid_data_object(
            self, db: AsyncSession, *, conversation_id: str
    ) -> Optional[MessageTable]:
        """获取指定会话中最新且data_object有效的数据"""
        query = (
            select(MessageTable)
            .where(
                MessageTable.conversation_id == conversation_id,
                # 同时排除数据库NULL和JSON null
                MessageTable.data_object.isnot(None),
                MessageTable.data_object != {},
                MessageTable.data_object != sa.text("'null'::jsonb"),  # 使用原生SQL表达式处理JSONB类型
            )
            .order_by(MessageTable.created_at.desc())
            .limit(1)
        )
        result = await db.execute(query)
        return result.scalars().first()

    def get_latest_valid_data_object_sync(
            self, db: ScopedSession, *, conversation_id: str
    ) -> Optional[MessageTable]:
        """获取指定会话中最新且data_object有效的数据"""
        query = (
            select(MessageTable)
            .where(
                MessageTable.conversation_id == conversation_id,
                # 同时排除数据库NULL和JSON null
                MessageTable.data_object.isnot(None),
                MessageTable.data_object != {},
                MessageTable.data_object != sa.text("'null'::jsonb"),  # 使用原生SQL表达式处理JSONB类型
            )
            .order_by(MessageTable.created_at.desc())
            .limit(1)
        )
        result = db.execute(query)
        return result.scalars().first()

    def create_sync(self, db: Session, obj_input: MessageCreate) -> MessageTable:
        """Synchronous version of create for thread usage"""
        db_obj = MessageTable(**obj_input.model_dump())
        db.add(db_obj)
        db.flush()  # Get the ID without committing
        db.refresh(db_obj)
        return db_obj

    def get_sync(self, db: Session, id: str) -> MessageTable | None:
        """Synchronous version of get"""
        return db.execute(select(MessageTable).where(MessageTable.id == id)).scalar_one_or_none()


message_curd = CRUDMessage(MessageTable)
