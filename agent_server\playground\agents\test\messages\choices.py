OPTIONS = """
[
    {"label": "Markdown 文档渲染", "value": "markdown"},
    {"label": "Radio 交互", "value": "radio_choices"},
    {"label": "Select 交互", "value": "select_choices"},
    {"label": "Mermaid 图表", "value": "mermaid"},
    {"label": "Thinking 深度思考", "value": "thinking"},
    {"label": "Thought Chain 思维链", "value": "thought_chain"},
    {"label": "State 实时状态更新", "value": "state"},
    {"label": "Form 表单", "value": "form"},
    {"label": "Tool 工具调用", "value": "tool"},
    {"label": "Error 错误", "value": "error"},
    {"label": "Widget 组件示例", "value": "widget"}
]
"""

RADIO_MESSAGE = f"""
<message-embedded>
    <widget>
        <code>@BuildIn/RadioGroupInteractive</code>
        <props>
            <options>
                {OPTIONS}
            </options>
            <value>{{{{state.radio.value}}}}</value>
            <valuePath>radio.value</valuePath>
        </props>
    </widget>

    <set>
      <strategy>replace</strategy>
      <path>radio.value</path>
      <value>null</value> 
    </set>
</message-embedded>
"""

radio_choices_message_chunks = [
    {
        "data": "请选择消息类型",
        "is_last": False,
        "package_type": 0,
        "is_new_package": True,
    },
    {
        "data": RADIO_MESSAGE,
        "is_last": True,
        "package_type": 0,
        "is_new_package": False,
    },
]


SELECT_MESSAGE = f"""
<message-embedded>
    <widget>
        <code>@BuildIn/SelectInteractive</code>
        <props>
            <options>
                {OPTIONS}
            </options>
            <value>{{{{state.select.value}}}}</value>
            <valuePath>select.value</valuePath>
        </props>
    </widget>

    <set>
      <strategy>replace</strategy>
      <path>select.value</path>
      <value>null</value> 
    </set>
</message-embedded>
"""

select_message_chunks = [
    {
        "data": "请选择消息类型",
        "is_last": False,
        "package_type": 0,
        "is_new_package": True,
    },
    {
        "data": SELECT_MESSAGE,
        "is_last": True,
        "package_type": 0,
        "is_new_package": False,
    },
]
