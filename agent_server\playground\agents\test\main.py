import asyncio
import re

from fastapi import API<PERSON><PERSON><PERSON>, <PERSON>, Depends, FastAPI
from fastapi.responses import StreamingResponse


from agent_server.core.auth.security import check_user
from agent_server.core.vo.user_vo import UserVO
from agent_server.core.message.transmitter import Transmitter

from .messages.markdown import create_markdown_message_chunks
from .messages.choices import radio_choices_message_chunks, select_message_chunks
from .messages.mermaid import create_mermaid_message_chunks
from .messages.thinking import create_thinking_message_chunks
from .messages.tool import create_tool_message_chunks
from .messages.error import create_error_message_chunks
from .messages.state import create_message_state_message_chunks
from .messages.thought_chain import thought_chain_event_generator
from .messages.form import create_form_message_chunks
from .messages.test import create_test_message_chunks
from .messages.widget import create_widget_message_chunks
from agent_server.core.message.generate import (
    run_generate_message_task,
    content_stream_generator,
    create_update_conversation,
    save_user_message,
)
from agent_server.core.services.database import db_manager
from agent_server.core.services.database.crud.conversation import conversation_curd

chunks = {
    "markdown": create_markdown_message_chunks(),
    "radio_choices": radio_choices_message_chunks,
    "select_choices": select_message_chunks,
    "mermaid": create_mermaid_message_chunks(),
    "thinking": create_thinking_message_chunks(),
    "tool": create_tool_message_chunks(),
    "error": create_error_message_chunks(),
    "state": create_message_state_message_chunks(),
    "thought_chain": None,
    "form": create_form_message_chunks(),
    "test": create_test_message_chunks(),
    "widget": create_widget_message_chunks(),
}


async def default_event_generator(transmitter: Transmitter, message_type: str = "all"):
    mock_message_chunks = chunks[message_type]
    yield transmitter.start()
    await asyncio.sleep(1)
    for chunk in mock_message_chunks:
        if chunk:
            if chunk.get("is_network_error") is True:
                return
            if chunk.get("is_error") is True:
                yield transmitter.send_error(chunk["data"])
                continue
            # Convert the chunk to JSON and format as SSE
            yield transmitter.send_message(
                data=chunk["data"],
                package_type=chunk["package_type"],
                is_last=chunk["is_last"],
                is_new_package=chunk["is_new_package"],
            )
        await asyncio.sleep(0.1)

    # Send a final message to indicate completion
    yield await transmitter.end()


class TestAgent:
    app: FastAPI
    router: APIRouter

    def __init__(self, app: FastAPI):
        self.app = app
        self.router = APIRouter()
        self.add_api(self.router)
        app.include_router(self.router, tags=["Test"])

    def add_api(self, router: APIRouter):
        @router.post("/api/chat/test")
        async def test(
            data: dict = Body(..., description="用户提问内容"),
            user: UserVO = Depends(check_user),
        ):
            agent_code = data.get("agent_code")
            hidden = data.get("hidden", False)
            message = data.get("message")
            extend_data = data.get(
                "extend_data", {}
            )  # 扩展数据，用于传递额外信息给后端
            # 根据conversation_id查询会话，如果没有则创建一个
            conversation_id = data.get("conversation_id")
            task_id = None

            async with db_manager.session() as session:
                conversation_list = await conversation_curd.get_by_conversation_id(
                    session, _id=conversation_id
                )
                if conversation_list:
                    task_id = conversation_list[0].task_id

            # 如果存在正在运行的任务，则直接加载消息
            # 不在这里创建对话，回复新的消息
            if not task_id:
                # 新建、更新会话
                conversation = conversation_list[0] if conversation_list else None
                (new_conversation_id, new_task_id) = await create_update_conversation(
                    message, conversation, agent_code, user.userId
                )
                conversation_id = new_conversation_id
                task_id = new_task_id
                # 保存用户消息
                await save_user_message(
                    message=message,
                    agent_code=agent_code,
                    conversation_id=conversation_id,
                    data=extend_data,
                    hidden=hidden,
                )
                message_type = self.get_message_type(message)

                def message_generator_fn(transmitter: Transmitter):
                    generator_fn = (
                        thought_chain_event_generator
                        if message_type == "thought_chain"
                        else default_event_generator
                    )
                    return generator_fn(transmitter, message_type)

                # Use the new sync task runner
                await run_generate_message_task(
                    task_id=task_id,
                    conversation_id=conversation_id,
                    agent_code=agent_code,
                    message_generator_fn=message_generator_fn,
                )
            return StreamingResponse(
                content_stream_generator(
                    task_id=task_id, conversation_id=conversation_id
                ),
                media_type="text/event-stream",
                headers={
                    "Cache-Control": "no-cache",
                    "Connection": "keep-alive",
                },
            )

    def get_message_type(self, user_message: str):
        msg = user_message.lower().strip()

        msg = re.sub(r"<message-embedded>[\w\W]+</message-embedded>", "", msg)
        types = chunks.keys()
        for msg_type in types:
            if msg_type in msg:
                return msg_type
        return "radio_choices"
