# Agent Server 项目概览

## 📊 项目统计

| 指标 | 数值 |
|------|------|
| **代码行数** | ~15,000+ |
| **模块数量** | 50+ |
| **API 接口** | 30+ |
| **数据表** | 15+ |
| **支持模型** | 10+ |
| **开发周期** | 6个月+ |

## 🏗️ 项目结构概览

```
agent-server/                    # 项目根目录
├── 📁 core/                    # 核心框架 (8,000+ 行)
│   ├── 🌐 api/                 # API 接口层
│   ├── 🔐 auth/                # 认证系统
│   ├── ⚙️ config/              # 配置管理
│   ├── 🗄️ services/            # 业务服务
│   ├── 🏭 factory/             # 工厂模式
│   ├── 🔗 langchain/           # LangChain 集成
│   ├── 📊 langgraph/           # LangGraph 集成
│   └── 💬 message/             # 消息处理
├── 📚 rags/                    # RAGs 知识库系统 (4,000+ 行)
│   ├── 🔌 api/                 # RAGs API
│   ├── ⚡ services/            # RAGs 服务
│   ├── 📋 models/              # 数据模型
│   ├── 🗄️ database/           # 数据库层
│   └── 🛠️ utils/               # 工具函数
├── 🤖 llmclient/              # LLM 客户端 (1,000+ 行)
├── 🔌 mcpclient/              # MCP 客户端 (500+ 行)
├── 🛠️ mcpserver/              # MCP 服务器 (800+ 行)
├── 🌐 gateway/                # API 网关 (300+ 行)
├── 🔧 plugins/                # 插件系统
├── ⚙️ config/                 # 配置文件
├── 📖 docs/                   # 项目文档
├── 🧪 tests/                  # 测试用例
└── 🐳 docker/                 # Docker 配置
```

## 🎯 核心功能模块

### 1. 智能体管理系统

**文件位置**: `core/factory/agent_factory.py`
**代码行数**: ~200 行
**核心功能**:
- 智能体动态加载和注册
- 多框架支持 (LangChain, LangGraph, Dify)
- 运行时配置管理
- 用户级别数据隔离

**关键类**:
```python
class AgentFactory:
    - load_agents()          # 加载智能体配置
    - get_sender()           # 获取智能体实例
    - register_sender_class() # 注册自定义发送器
```

### 2. LLM 客户端系统

**文件位置**: `llmclient/llm_client.py`
**代码行数**: ~300 行
**核心功能**:
- 多模型服务统一接入
- 工具动态发现与注册
- 流式对话生命周期管理
- 智能体执行引擎构建

**关键类**:
```python
class LLMClient:
    - build_llm()           # 构建大模型引擎
    - init_mcp_clients()    # 初始化MCP客户端
    - _init_tools()         # 工具发现与注册
    - _init_agent()         # 智能体引擎装配
```

### 3. RAGs 知识库系统

**文件位置**: `rags/` 目录
**代码行数**: ~4,000 行
**核心功能**:
- 文档向量化处理
- 多种检索策略 (向量、全文、混合)
- 重排序优化
- 知识库管理

**关键服务**:
```python
# 核心服务类
- EmbeddingService      # 嵌入服务
- VectorStore          # 向量存储
- RetrievalService     # 检索服务
- RerankService        # 重排序服务
- KnowledgeBaseService # 知识库管理
```

### 4. MCP 协议支持

**文件位置**: `mcpclient/`, `mcpserver/`
**代码行数**: ~1,300 行
**核心功能**:
- MCP 协议完整实现
- 工具标准化接口
- 多种传输方式支持
- 动态工具发现

**关键组件**:
```python
# MCP 客户端
class McpClient:
    - list_tools()      # 获取工具列表
    - execute_tool()    # 执行工具调用

# MCP 工厂
class McpFactory:
    - create_mcp_clients() # 创建客户端
    - close_clients()      # 关闭连接
```

### 5. 认证授权系统

**文件位置**: `core/auth/`
**代码行数**: ~400 行
**核心功能**:
- 可插拔认证适配器
- 基于角色的访问控制
- JWT Token 管理
- 权限验证中间件

**关键组件**:
```python
# 认证适配器
class AuthAdapter:
    - parse_user_data()  # 解析用户数据

# 安全检查
- check_user()          # 用户认证检查
- pre_authorize()       # 权限预检查
```

### 6. 路由注册系统

**文件位置**: `core/api/router_registry.py`
**代码行数**: ~300 行
**核心功能**:
- 动态路由发现
- 插件化路由注册
- 配置文件和装饰器双重支持
- 优先级管理

**关键功能**:
```python
# 路由注册
- router_register()     # 路由注册函数
- apply_routers_to_app() # 应用路由到应用
- load_routers_from_config() # 从配置加载
```

## 📊 数据库设计

### 核心数据表

| 表名 | 用途 | 主要字段 | 记录数量级 |
|------|------|----------|-----------|
| `conversation` | 会话管理 | id, title, user_id, agent_code | 10万+ |
| `message` | 消息存储 | id, conversation_id, content, type | 100万+ |
| `ai_agent` | 智能体配置 | agent_code, name, target_type | 100+ |
| `prompt_template` | 提示词模板 | id, title, content, type | 1000+ |
| `popular_question` | 热门问题 | id, question, heat_count | 500+ |
| `ai_knowledge_base` | 知识库 | id, name, collection_name | 100+ |
| `ai_document` | 文档管理 | id, kb_id, title, content | 10万+ |
| `ai_document_chunk` | 文档分段 | id, doc_id, content, embedding | 100万+ |

### 数据关系图

```mermaid
erDiagram
    CONVERSATION ||--o{ MESSAGE : contains
    AI_AGENT ||--o{ CONVERSATION : uses
    AI_AGENT ||--o{ PROMPT_TEMPLATE : has
    AI_KNOWLEDGE_BASE ||--o{ AI_DOCUMENT : contains
    AI_DOCUMENT ||--o{ AI_DOCUMENT_CHUNK : splits_into
    
    CONVERSATION {
        string id PK
        string title
        int user_id
        string current_agent_code FK
        timestamp created_at
    }
    
    MESSAGE {
        string id PK
        string conversation_id FK
        string agent_code
        jsonb content
        string message_type
    }
    
    AI_AGENT {
        int id PK
        string agent_code UK
        string agent_name
        string target_type
        text prompt_text
    }
```

## 🔧 技术架构

### 分层架构

```mermaid
graph TB
    subgraph "表现层"
        A[REST API]
        B[WebSocket]
        C[管理后台]
    end
    
    subgraph "业务层"
        D[智能体服务]
        E[对话服务]
        F[知识库服务]
        G[认证服务]
    end
    
    subgraph "数据层"
        H[PostgreSQL]
        I[Redis]
        J[Milvus]
        K[文件存储]
    end
    
    subgraph "外部服务"
        L[LLM API]
        M[嵌入模型]
        N[业务系统]
    end
    
    A --> D
    A --> E
    A --> F
    B --> E
    C --> G
    D --> H
    E --> I
    F --> J
    D --> L
    F --> M
    G --> N
```

### 技术选型理由

| 技术 | 选择理由 | 替代方案 |
|------|----------|----------|
| **FastAPI** | 高性能、自动文档、类型安全 | Flask, Django |
| **PostgreSQL** | 企业级、JSONB支持、全文检索 | MySQL, MongoDB |
| **Redis** | 高性能缓存、会话存储 | Memcached |
| **Milvus** | 专业向量数据库、高性能检索 | Pinecone, Weaviate |
| **LangChain** | 丰富的AI生态、工具集成 | LlamaIndex |
| **Pydantic** | 数据验证、类型安全 | Marshmallow |
| **SQLModel** | 类型安全的ORM、FastAPI集成 | SQLAlchemy |

## 📈 性能指标

### API 性能

| 接口类型 | 平均响应时间 | P95响应时间 | QPS | 并发数 |
|----------|-------------|-------------|-----|--------|
| 用户认证 | 50ms | 100ms | 1000 | 100 |
| 智能体列表 | 30ms | 60ms | 2000 | 200 |
| 发起对话 | 200ms | 500ms | 500 | 50 |
| 知识库检索 | 100ms | 200ms | 800 | 80 |
| 文档上传 | 2s | 5s | 50 | 10 |

### 系统资源

| 组件 | CPU使用率 | 内存使用 | 磁盘I/O | 网络I/O |
|------|-----------|----------|---------|---------|
| Agent Server | 30% | 2GB | 低 | 中 |
| PostgreSQL | 20% | 1GB | 中 | 低 |
| Redis | 10% | 512MB | 低 | 低 |
| Milvus | 40% | 4GB | 高 | 中 |

## 🔄 开发流程

### Git 工作流

```mermaid
gitgraph
    commit id: "Initial"
    branch develop
    checkout develop
    commit id: "Feature A"
    commit id: "Feature B"
    branch feature/new-agent
    checkout feature/new-agent
    commit id: "Add agent"
    commit id: "Add tests"
    checkout develop
    merge feature/new-agent
    checkout main
    merge develop
    commit id: "Release v1.0"
```

### 代码质量

| 指标 | 目标值 | 当前值 |
|------|--------|--------|
| **测试覆盖率** | >80% | 75% |
| **代码复杂度** | <10 | 8.5 |
| **文档覆盖率** | >90% | 85% |
| **静态检查** | 0 错误 | 2 警告 |

## 🚀 部署架构

### 生产环境

```mermaid
graph TB
    subgraph "负载均衡层"
        A[Nginx]
    end
    
    subgraph "应用层"
        B[Agent Server 1]
        C[Agent Server 2]
        D[Agent Server 3]
    end
    
    subgraph "数据层"
        E[PostgreSQL Master]
        F[PostgreSQL Slave]
        G[Redis Cluster]
        H[Milvus Cluster]
    end
    
    subgraph "监控层"
        I[Prometheus]
        J[Grafana]
        K[ELK Stack]
    end
    
    A --> B
    A --> C
    A --> D
    B --> E
    C --> E
    D --> E
    E --> F
    B --> G
    C --> G
    D --> G
    B --> H
    C --> H
    D --> H
    B --> I
    C --> I
    D --> I
    I --> J
    B --> K
    C --> K
    D --> K
```

### 容器化部署

- **Docker**: 应用容器化
- **Docker Compose**: 本地开发环境
- **Kubernetes**: 生产环境编排
- **Helm**: 配置管理

## 📋 项目里程碑

### 已完成功能 ✅

- [x] 核心框架搭建
- [x] 多智能体支持
- [x] MCP 协议集成
- [x] RAGs 知识库系统
- [x] 认证授权系统
- [x] 路由注册系统
- [x] API 接口完善
- [x] 数据库设计
- [x] Docker 部署

### 进行中功能 🚧

- [ ] 性能优化
- [ ] 监控系统
- [ ] 自动化测试
- [ ] 文档完善

### 计划功能 📅

- [ ] 微服务拆分
- [ ] 多语言支持
- [ ] 插件市场
- [ ] 可视化配置
- [ ] 移动端支持

## 🎯 未来规划

### 短期目标 (3个月)

1. **性能优化**: 提升API响应速度50%
2. **监控完善**: 建立完整的监控体系
3. **测试覆盖**: 达到90%测试覆盖率
4. **文档完善**: 完成所有API文档

### 中期目标 (6个月)

1. **微服务化**: 拆分为独立的微服务
2. **插件生态**: 建立插件开发生态
3. **多租户**: 支持多租户部署
4. **国际化**: 支持多语言界面

### 长期目标 (1年)

1. **云原生**: 完整的云原生架构
2. **AI优化**: 智能化运维和优化
3. **生态建设**: 建立开发者社区
4. **商业化**: 提供企业级服务

---

**Agent Server** - 构建下一代 AI 应用的企业级框架 🚀
