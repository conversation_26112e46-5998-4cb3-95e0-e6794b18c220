"""
测试提示词模板功能
"""
import unittest
import asyncio
from datetime import datetime, timezone

from agent_server.core.services.database.schemas.prompt_template import (
    PromptTemplateTable,
    PromptTemplateCreate,
    PromptTemplateUpdate,
)
from agent_server.core.services.database.crud.prompt_template import prompt_template_crud
from agent_server.core.services.database.base import DatabaseManager


class TestPromptTemplate(unittest.TestCase):
    """提示词模板功能测试"""

    @classmethod
    def setUpClass(cls):
        """测试类级别的设置"""
        cls.loop = asyncio.new_event_loop()
        asyncio.set_event_loop(cls.loop)
        # 为测试创建独立的数据库管理器实例
        cls.db_manager = DatabaseManager()

    @classmethod
    def tearDownClass(cls):
        """测试类级别的清理"""
        # 关闭事件循环
        cls.loop.close()

    def setUp(self):
        """每个测试方法执行前的设置"""
        # 创建测试用的模板
        self.sample_templates = self.loop.run_until_complete(self._create_sample_templates())

    def tearDown(self):
        """每个测试方法执行后的清理"""
        # 清理：删除测试数据
        self.loop.run_until_complete(self._cleanup_sample_templates())

    async def _create_sample_templates(self):
        """创建测试用的模板"""
        templates = []
        async with self.db_manager.session() as session:
            # 创建用户类型模板
            user_template = PromptTemplateCreate(
                type="user",
                title="用户测试模板",
                description="这是一个用于测试的用户模板",
                prompt="请帮我分析以下内容：{content}",
                group="test_group",
                preview="分析内容模板",
                order=1
            )
            templates.append(await prompt_template_crud.create(db=session, obj_input=user_template))

            # 创建系统类型模板
            system_template = PromptTemplateCreate(
                type="system",
                title="系统测试模板",
                description="这是一个用于测试的系统模板",
                prompt="你是一个专业的助手，请按照以下要求回答问题：{requirements}",
                group="test_group",
                preview="专业助手模板",
                order=2
            )
            templates.append(await prompt_template_crud.create(db=session, obj_input=system_template))

            # 创建另一个分组的模板
            another_template = PromptTemplateCreate(
                type="user",
                title="另一个测试模板",
                description="这是另一个分组的测试模板",
                prompt="请总结以下内容：{content}",
                group="another_group",
                preview="内容总结模板",
                order=3
            )
            templates.append(await prompt_template_crud.create(db=session, obj_input=another_template))

        return templates

    async def _cleanup_sample_templates(self):
        """清理测试数据"""
        try:
            async with self.db_manager.session() as session:
                for template in self.sample_templates:
                    await prompt_template_crud.remove(db=session, _id=template.id)
        except Exception:
            pass  # 如果已经被删除则忽略

    def test_create_prompt_template(self):
        """测试创建提示词模板"""
        self.loop.run_until_complete(self._test_create_prompt_template())

    async def _test_create_prompt_template(self):
        """测试创建提示词模板的异步实现"""
        async with self.db_manager.session() as session:
            template_data = PromptTemplateCreate(
                type="user",
                title="新建测试模板",
                description="这是一个新建的测试模板",
                prompt="请处理以下任务：{task}",
                group="new_group",
                preview="任务处理模板",
                order=10
            )
            
            created_template = await prompt_template_crud.create(db=session, obj_input=template_data)
            
            self.assertIsNotNone(created_template)
            self.assertEqual(created_template.type, "user")
            self.assertEqual(created_template.title, "新建测试模板")
            self.assertEqual(created_template.group, "new_group")
            self.assertIsNotNone(created_template.created_at)
            self.assertIsNotNone(created_template.updated_at)
            
            # 清理
            await prompt_template_crud.remove(db=session, _id=created_template.id)

    def test_get_by_type(self):
        """测试按类型查询模板"""
        self.loop.run_until_complete(self._test_get_by_type())

    async def _test_get_by_type(self):
        """测试按类型查询模板的异步实现"""
        async with self.db_manager.session() as session:
            # 查询用户类型模板
            user_templates = await prompt_template_crud.get_by_type(db=session, template_type="user")
            user_template_ids = [t.id for t in user_templates]
            
            # 应该包含我们创建的用户模板
            self.assertIn(self.sample_templates[0].id, user_template_ids)
            self.assertIn(self.sample_templates[2].id, user_template_ids)
            
            # 查询系统类型模板
            system_templates = await prompt_template_crud.get_by_type(db=session, template_type="system")
            system_template_ids = [t.id for t in system_templates]
            
            # 应该包含我们创建的系统模板
            self.assertIn(self.sample_templates[1].id, system_template_ids)
            
            # 验证排序（按order字段升序）
            for i in range(len(user_templates) - 1):
                self.assertLessEqual(user_templates[i].order, user_templates[i + 1].order)

    def test_get_by_group(self):
        """测试按分组查询模板"""
        self.loop.run_until_complete(self._test_get_by_group())

    async def _test_get_by_group(self):
        """测试按分组查询模板的异步实现"""
        async with self.db_manager.session() as session:
            # 查询test_group分组的模板
            test_group_templates = await prompt_template_crud.get_by_group(db=session, group="test_group")
            test_group_ids = [t.id for t in test_group_templates]
            
            # 应该包含前两个模板
            self.assertIn(self.sample_templates[0].id, test_group_ids)
            self.assertIn(self.sample_templates[1].id, test_group_ids)
            self.assertNotIn(self.sample_templates[2].id, test_group_ids)

    def test_get_by_type_and_group(self):
        """测试按类型和分组查询模板"""
        self.loop.run_until_complete(self._test_get_by_type_and_group())

    async def _test_get_by_type_and_group(self):
        """测试按类型和分组查询模板的异步实现"""
        async with self.db_manager.session() as session:
            # 查询test_group分组的用户类型模板
            templates = await prompt_template_crud.get_by_type_and_group(
                db=session, template_type="user", group="test_group"
            )
            template_ids = [t.id for t in templates]
            
            # 应该只包含第一个模板
            self.assertIn(self.sample_templates[0].id, template_ids)
            self.assertNotIn(self.sample_templates[1].id, template_ids)  # 系统类型
            self.assertNotIn(self.sample_templates[2].id, template_ids)  # 不同分组

    def test_search_by_title_or_description(self):
        """测试按标题或描述搜索模板"""
        self.loop.run_until_complete(self._test_search_by_title_or_description())

    async def _test_search_by_title_or_description(self):
        """测试按标题或描述搜索模板的异步实现"""
        async with self.db_manager.session() as session:
            # 搜索包含"用户"的模板
            templates = await prompt_template_crud.search_by_title_or_description(
                db=session, search_term="用户"
            )
            template_ids = [t.id for t in templates]
            
            # 应该找到第一个模板
            self.assertIn(self.sample_templates[0].id, template_ids)

    def test_get_groups(self):
        """测试获取所有分组"""
        self.loop.run_until_complete(self._test_get_groups())

    async def _test_get_groups(self):
        """测试获取所有分组的异步实现"""
        async with self.db_manager.session() as session:
            groups = await prompt_template_crud.get_groups(db=session)
            
            # 应该包含我们创建的分组
            self.assertIn("test_group", groups)
            self.assertIn("another_group", groups)

    def test_update_prompt_template(self):
        """测试更新提示词模板"""
        self.loop.run_until_complete(self._test_update_prompt_template())

    async def _test_update_prompt_template(self):
        """测试更新提示词模板的异步实现"""
        async with self.db_manager.session() as session:
            # 获取要更新的模板
            template = await prompt_template_crud.get(db=session, _id=self.sample_templates[0].id)
            original_updated_at = template.updated_at
            
            # 更新模板
            update_data = PromptTemplateUpdate(
                title="更新后的标题",
                description="更新后的描述"
            )
            
            updated_template = await prompt_template_crud.update(
                db=session, db_obj=template, obj_in=update_data
            )
            
            self.assertEqual(updated_template.title, "更新后的标题")
            self.assertEqual(updated_template.description, "更新后的描述")
            self.assertGreater(updated_template.updated_at, original_updated_at)

    def test_template_validation(self):
        """测试模板字段验证"""
        # 测试type字段验证
        with self.assertRaises(ValueError):
            PromptTemplateCreate(
                type="invalid_type",  # 无效的类型
                title="测试",
                description="测试",
                prompt="测试",
                group="测试",
                preview="测试"
            )

    def test_template_model_fields(self):
        """测试模板模型字段"""
        template = PromptTemplateTable(
            type="user",
            title="测试模板",
            description="测试描述",
            prompt="测试提示词",
            group="测试分组",
            preview="测试预览",
            order=1
        )
        
        # 确认所有必需字段存在
        self.assertTrue(hasattr(template, 'type'))
        self.assertTrue(hasattr(template, 'title'))
        self.assertTrue(hasattr(template, 'description'))
        self.assertTrue(hasattr(template, 'prompt'))
        self.assertTrue(hasattr(template, 'group'))
        self.assertTrue(hasattr(template, 'preview'))
        self.assertTrue(hasattr(template, 'order'))
        self.assertTrue(hasattr(template, 'created_at'))
        self.assertTrue(hasattr(template, 'updated_at'))
        
        # 确认默认值
        self.assertEqual(template.order, 1)


if __name__ == '__main__':
    unittest.main()
