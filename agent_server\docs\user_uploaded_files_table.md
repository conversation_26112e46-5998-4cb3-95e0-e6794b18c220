# User Uploaded Files Table Documentation

## Overview

The `user_uploaded_files` table is designed to track and manage files uploaded by users to the system, with integration to the RAGFlow document processing system.

## Table Schema

### Table Name: `user_uploaded_files`

### Column Definitions

| Column Name | Data Type | Constraints | Description |
|-------------|-----------|-------------|-------------|
| `id` | VARCHAR | PRIMARY KEY | Unique identifier for each uploaded file record (UUID hex format) |
| `user_id` | INTEGER | NOT NULL, INDEXED | Foreign key reference to the user who uploaded the file |
| `name` | VARCHAR | NOT NULL | Original filename as provided by the user during upload |
| `hash` | VARCHAR | NOT NULL, INDEXED | SHA256 hash of the file content, used for deduplication |
| `size` | INTEGER | NOT NULL, CHECK > 0 | File size in bytes (must be positive) |
| `type` | VARCHAR(50) | NOT NULL | MIME type of the uploaded file (e.g., 'application/pdf', 'text/plain') |
| `rag_file_id` | VARCHAR | NOT NULL, INDEXED | Document ID returned by RAG system (critical association field) |
| `rag_service` | VARCHAR(50) | NOT NULL, DEFAULT 'ragflow' | RAG service type identifier (e.g., 'ragflow', 'langchain', 'llamaindex') |
| `status` | ENUM | NOT NULL | Current processing status: 'uploading', 'parsing', 'ready', 'error' |
| `error_message` | VARCHAR | NULLABLE | Error details when file parsing fails (only populated when status is 'error') |
| `created_at` | TIMESTAMP WITH TIME ZONE | NOT NULL, DEFAULT CURRENT_TIMESTAMP | Record creation timestamp |
| `updated_at` | TIMESTAMP WITH TIME ZONE | NOT NULL, DEFAULT CURRENT_TIMESTAMP | Last modification timestamp |

### Indexes

The following indexes are created for performance optimization:

1. **Primary Index**: `id` (Primary Key)
2. **Single Column Indexes**:
   - `ix_user_uploaded_files_user_id` on `user_id`
   - `ix_user_uploaded_files_hash` on `hash`
   - `ix_user_uploaded_files_rag_file_id` on `rag_file_id`

3. **Composite Indexes**:
   - `idx_user_files` on `(user_id, status)` - for filtering user files by status
   - `idx_file_hash_user` on `(hash, user_id)` - for deduplication checks
   - `idx_ragflow_doc` on `rag_file_id` - for RAGFlow integration queries

### Constraints

1. **Check Constraint**: `check_positive_file_size` ensures `size > 0`
2. **Enum Constraint**: `status` field is restricted to: 'uploading', 'parsing', 'ready', 'error'

## File Processing Workflow

### Status Flow

```
uploading → parsing → ready
    ↓         ↓
   error ← error
```

1. **uploading**: File is being uploaded to the system
2. **parsing**: File has been uploaded and is being processed by RAGFlow
3. **ready**: File has been successfully processed and is available for use
4. **error**: An error occurred during upload or processing

### Typical Usage Pattern

1. **File Upload**:
   ```python
   # Create new file record
   file_record = UserUploadedFilesCreate(
       user_id=user_id,
       name=original_filename,
       hash=calculate_sha256(file_content),
       size=len(file_content),
       type=detect_mime_type(file_content),
       rag_file_id=ragflow_response.doc_id,
       rag_service="ragflow",  # Specify the RAG service provider
       status=FileStatus.UPLOADING
   )
   ```

2. **Status Updates**:
   ```python
   # Update to parsing status
   await user_uploaded_files_crud.update_status(
       db, file_id=file_id, status=FileStatus.PARSING
   )
   
   # Update to ready status
   await user_uploaded_files_crud.update_status(
       db, file_id=file_id, status=FileStatus.READY
   )
   
   # Update to error status with message
   await user_uploaded_files_crud.update_status(
       db, file_id=file_id, status=FileStatus.ERROR, 
       error_message="Failed to parse PDF: corrupted file"
   )
   ```

## CRUD Operations

The system provides comprehensive CRUD operations through `user_uploaded_files_crud`:

### Create
```python
file_record = await user_uploaded_files_crud.create(db, obj_in=file_data)
```

### Read Operations
```python
# Get by ID
file_record = await user_uploaded_files_crud.get(db, id=file_id)

# Get by user ID
user_files = await user_uploaded_files_crud.get_by_user_id(db, user_id=123)

# Get by file hash (for deduplication)
existing_file = await user_uploaded_files_crud.get_by_file_hash(db, file_hash="abc123")

# Get by RAGFlow document ID
file_record = await user_uploaded_files_crud.get_by_rag_file_id(db, rag_file_id="doc123")

# Get by status
parsing_files = await user_uploaded_files_crud.get_by_status(db, status=FileStatus.PARSING)
```

### Update
```python
# General update
updated_file = await user_uploaded_files_crud.update(db, db_obj=file_record, obj_in=update_data)

# Status-specific update
updated_file = await user_uploaded_files_crud.update_status(
    db, file_id=file_id, status=FileStatus.READY
)
```

### Delete
```python
deleted_file = await user_uploaded_files_crud.delete(db, id=file_id)
```

### Statistics
```python
file_count = await user_uploaded_files_crud.count_by_user(db, user_id=123)
```

## Integration Points

### RAG Service Integration
- The `rag_file_id` field is critical for associating uploaded files with RAG system documents
- The `rag_service` field identifies which RAG service provider is being used (default: 'ragflow')
- This enables document retrieval and content search functionality across different RAG providers
- Status updates should reflect the respective RAG service processing states
- Future integration with multiple RAG providers (e.g., LangChain, LlamaIndex) is supported through the `rag_service` field

### User Management
- The `user_id` field should reference the existing user system
- Consider adding foreign key constraints if a users table exists

### File Deduplication
- Use `hash` to detect duplicate uploads
- Implement logic to reuse existing processed files for the same content

## Migration

To apply the database migration:

```bash
# Run the migration
alembic upgrade head
```

To rollback the migration:

```bash
# Rollback to previous version
alembic downgrade -1
```

## Testing

Run the test suite to verify the implementation:

```bash
python -m unittest test.test_user_uploaded_files -v
```

## Performance Considerations

1. **Indexing**: All frequently queried columns are indexed
2. **Composite Indexes**: Optimized for common query patterns
3. **File Size Constraint**: Prevents invalid data entry
4. **Status Enum**: Ensures data consistency and enables efficient filtering

## Security Considerations

1. **User Isolation**: Always filter by `user_id` to prevent unauthorized access
2. **File Hash Verification**: Use SHA256 hashes to detect file tampering
3. **Error Message Sanitization**: Ensure error messages don't expose sensitive information
4. **File Type Validation**: Validate MIME types to prevent malicious uploads
