# 路由注册系统使用指南

## 概述

路由注册系统提供了一种灵活的方式来动态注册路由模块，支持两种注册方式：

1. **函数调用方式**：在模块中直接调用 `router_register()` 函数
2. **配置文件方式**：在 `config/application.yml` 中手动配置路由信息

## 特性

- ✅ **自动发现**：自动扫描指定包中的路由模块
- ✅ **配置优先**：手动配置的路由优先级高于自动发现
- ✅ **灵活配置**：支持前缀、标签、依赖项、优先级等配置
- ✅ **错误容忍**：导入失败不会影响其他模块的加载
- ✅ **向后兼容**：不影响现有的核心路由注册方式

## 使用方法

### 方法1：函数调用方式（推荐）

在路由模块的顶部调用 `router_register()` 函数：

```python
# rags/api/knowledge_base_api.py
from fastapi import APIRouter

# 导入路由注册函数
try:
    from agent_server.core.api.router_registry import router_register
    # 注册路由模块
    router_register(
        prefix="/rags",
        tags=["RAGs知识库"],
        description="RAGs知识库管理API",
        priority=100
    )
except ImportError:
    # 如果无法导入，跳过注册（在独立运行时）
    pass

# 创建路由器
router = APIRouter(tags=["RAGs知识库"])

# 定义路由
@router.get("/health")
async def health_check():
    return {"status": "healthy"}
```

### 方法2：配置文件方式

在 `config/application.yml` 中配置：

```yaml
# 路由注册配置
routers:
  # 自动发现配置
  auto_discovery:
    enabled: true  # 是否启用自动发现
    packages:  # 要扫描的包列表
      - "rags"
      - "plugins"
  
  # 手动注册的路由（优先级高于自动发现）
  manual:
    - module_path: "rags.api.knowledge_base_api"
      router_name: "router"
      prefix: "/rags"
      tags: ["RAGs知识库"]
      enabled: true
      description: "RAGs知识库管理API"
      priority: 100
    
    - module_path: "plugins.example_plugin"
      router_name: "router"
      prefix: "/example"
      tags: ["示例插件"]
      enabled: true
      description: "示例插件API"
      priority: 200
```

## 配置参数说明

| 参数 | 类型 | 默认值 | 说明 |
|------|------|--------|------|
| `module_path` | str | 必填 | 模块路径，如 "rags.api.knowledge_base_api" |
| `router_name` | str | "router" | 路由对象名称 |
| `prefix` | str | "" | 路由前缀 |
| `tags` | List[str] | [] | 路由标签 |
| `dependencies` | List[Any] | [] | 依赖项 |
| `enabled` | bool | True | 是否启用 |
| `description` | str | "" | 描述信息 |
| `priority` | int | 100 | 优先级，数字越小优先级越高 |

## 优先级规则

1. **手动配置** > **函数调用注册**
2. 相同优先级按模块路径字母顺序排序
3. 优先级数字越小，注册顺序越靠前

## 最佳实践

### 1. 模块结构

```
your_module/
├── __init__.py
├── api/
│   ├── __init__.py
│   └── your_api.py  # 在这里使用 router_register()
└── services/
    └── your_service.py
```

### 2. 错误处理

始终使用 try-except 包装导入，确保模块可以独立运行：

```python
try:
    from agent_server.core.api.router_registry import router_register
    router_register(prefix="/your-module", tags=["Your Module"])
except ImportError:
    # 独立运行时跳过注册
    pass
```

### 3. 路由类型

如果需要注册到公开路由（不需要权限验证），在模块中设置：

```python
# 标记为公开路由
__router_type__ = 'public'
```

### 4. 命名规范

- 模块路径使用小写和下划线：`your_module.api.your_api`
- 路由前缀使用短横线：`/your-module`
- 标签使用中文或英文：`["Your Module", "你的模块"]`

## 示例项目

### 创建一个新的插件模块

1. 创建目录结构：
```
plugins/
├── __init__.py
└── my_plugin/
    ├── __init__.py
    └── api.py
```

2. 在 `plugins/my_plugin/api.py` 中：
```python
from fastapi import APIRouter
from fastapi.responses import JSONResponse

# 注册路由
try:
    from agent_server.core.api.router_registry import router_register
    router_register(
        prefix="/my-plugin",
        tags=["我的插件"],
        description="这是我的自定义插件",
        priority=150
    )
except ImportError:
    pass

router = APIRouter()

@router.get("/hello")
async def hello():
    return JSONResponse({"message": "Hello from my plugin!"})
```

3. 更新 `config/application.yml`：
```yaml
routers:
  auto_discovery:
    enabled: true
    packages:
      - "rags"
      - "plugins"  # 会自动发现 plugins.my_plugin.api
```

## 故障排除

### 常见问题

1. **模块导入失败**
   - 检查模块路径是否正确
   - 确保模块中有 `router` 对象
   - 查看日志中的错误信息

2. **路由未注册**
   - 检查 `enabled` 是否为 `true`
   - 确认模块在扫描包列表中
   - 验证 `router_register()` 调用是否成功

3. **路由冲突**
   - 检查路由前缀是否重复
   - 调整优先级避免冲突

### 调试方法

运行测试脚本查看注册状态：

```bash
python test_router_registry.py
```

查看日志输出了解加载过程：

```bash
# 启动应用时查看日志
python main.py
```

## 总结

路由注册系统提供了一种现代化、灵活的方式来管理路由模块，特别适合：

- 插件化架构
- 微服务模块化
- 第三方模块集成
- 动态功能开关

通过合理使用这个系统，可以让你的项目更加模块化和可维护。
