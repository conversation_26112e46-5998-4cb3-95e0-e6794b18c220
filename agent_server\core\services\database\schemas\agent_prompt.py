from datetime import datetime, timezone
from typing import Optional
from sqlmodel import Field, SQLModel, Column, DateTime
from pydantic import field_validator
from sqlalchemy import Text
# Agent Prompt 模型
# <AUTHOR> xiangjh

class AgentPromptBase(SQLModel):
    """Agent 级提示词基础模型"""

    agent_code: str = Field(nullable=False, description="Agent 编码")
    p_type: str = Field(nullable=False, description="提示词类别：user 或 system")
    title: Optional[str] = Field(default=None, description="模板名称")
    node_code: Optional[str] = Field(default=None, description="节点代码")
    prompt: Optional[str] = Field(
        default=None,
        sa_column=Column(Text),
        description="模板提示词正文（长文本）",
    )
    status: int = Field(default=0, description="状态，数字类型，默认值为0")
    create_time: datetime = Field(
        default_factory=lambda: datetime.now(timezone.utc),
        sa_column=Column(DateTime(timezone=True)),
        description="创建时间",
    )
    update_time: Optional[datetime] = Field(
        default_factory=lambda: datetime.now(timezone.utc),
        sa_column=Column(DateTime(timezone=True)),
        description="更新时间",
    )
    create_by: str = Field(description="创建者")
    update_by: Optional[str] = Field(description="修改者")

    @field_validator("p_type")
    @classmethod
    def validate_type(cls, v):
        """验证p_type字段只能是user或system"""
        if v not in ["user", "system"]:
            raise ValueError('p_type must be either "user" or "system"')
        return v


class AgentPromptTable(AgentPromptBase, table=True):
    """数据库中的 Agent Prompt 模型"""

    __tablename__ = "agent_prompt"

    id: int = Field(default=None, primary_key=True)


class AgentPromptCreate(AgentPromptBase):
    """创建 Agent Prompt 模型"""

    pass


class AgentPromptUpdate(SQLModel):
    """更新 Agent Prompt 模型"""

    agent_code: Optional[str] = None
    p_type: Optional[str] = None
    title: Optional[str] = None
    node_code: Optional[str] = None
    prompt: Optional[str] = None
    status: Optional[int] = None
    update_time: datetime = Field(
        default_factory=lambda: datetime.now(timezone.utc), description="更新时间"
    )

    @field_validator("p_type")
    @classmethod
    def validate_type_optional(cls, v):
        if v is not None and v not in ["user", "system"]:
            raise ValueError('p_type must be either "user" or "system"')
        return v


class AgentPromptRead(AgentPromptBase):
    """API 响应中的 Agent Prompt 模型"""

    id: int

