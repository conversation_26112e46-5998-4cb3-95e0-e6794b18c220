from agent_server.core.langgraph.graph_builder import BaseGraphBuilder,AgentState
from typing import List, Dict, Any, Annotated, Sequence, Optional, TypedDict, Literal
from langchain_core.messages import BaseMessage, HumanMessage, AIMessage
from langchain_openai import ChatOpenAI
from langchain_core.prompts import Chat<PERSON>romptTemplate, MessagesPlaceholder
from langchain_core.tools import BaseTool
from langgraph.graph import StateGraph, START, END
from langgraph.graph.message import add_messages
from langgraph.checkpoint.memory import MemorySaver
from langgraph.prebuilt import ToolNode, tools_condition, create_react_agent
from langchain_core.runnables import RunnableConfig
from agent_server.core.config.app_config import ModelConfig
from agent_server.core.config.app_logger import logger
from agent_server.core.template.template_config import get_prompt_template
from langgraph.types import Command, interrupt
import json
from langchain_core.tools import tool

# 示例代码，仅做参考 
# <AUTHOR> xiangjh

@tool
def query_company_info(company_name: str = None) -> str:
    """
    查询指定公司名称查询公司信息，如果查询到多笔，则返回列表，否则返回空列表。
    
    Parameters:
        company_name (str, optional): 公司名称，如果不提供则返回一些示例数据
        
    Returns:
        str: JSON格式的公司收入数据列表
    """
    all_data = [
        {"companycode": "000001", "companyname": "苹果公司"},
        {"companycode": "000002", "companyname": "微软公司"},
        {"companycode": "000003", "companyname": "谷歌公司"},
        {"companycode": "000004", "companyname": "亚马逊"},    
        {"companycode": "000005", "companyname": "特斯拉"},
        {"companycode": "000006", "companyname": "英伟达"},
        {"companycode": "000007", "companyname": "阿里巴巴"},
        {"companycode": "000008", "companyname": "腾讯"},
        {"companycode": "000009", "companyname": "深圳万科"},
        {"companycode": "000010", "companyname": "成都万科"},
    ]

    # 如果提供了公司名称，则筛选匹配的公司
    if company_name:
        filtered_data = [item for item in all_data if company_name in item["companyname"]]
        if not filtered_data:
            # 如果没有匹配的公司，返回一条默认数据
            filtered_data = [{"companyname": company_name, "companytype": "未知", "companyaddress": "未知"}]
        result = filtered_data
    else:
        # 如果没有提供公司名称，返回空数据
        result = []

    fianResult = {
            "data": result,
            "success": "true",
            "message": "查询成功"
        }         
    logger.warning(f"调用tools查询公司信息: {fianResult}")
    return fianResult 



class CustomGraphBuilder(BaseGraphBuilder):
    """自定义LangGraph构建器"""

    def call_llm(self, state: AgentState, tools: List[BaseTool], model_config: ModelConfig, agent_id: str, node_name: str) -> Dict[str, Any]:
            """Agent节点处理函数

            Args:
                state (AgentState): Agent当前状态，包含消息历史等信息
                agent_id (str): 外层传入的 Agent 标识
                node_name (str): 当前节点代码

            Returns:
                Dict[str, Any]: 处理结果，包含消息和中间步骤
            """
            prompt = self.build_prompt(agent_id, node_name)
            #prompt = ChatPromptTemplate.from_messages([("system", system_prompt), MessagesPlaceholder("messages")])
            llm = self.build_llm(model_config)
            #tools = [query_company_info]
            
            llm_with_tools =  prompt | llm.bind_tools(tools)
    
            # 获取消息历史
            messages = state["messages"]
            logger.info(f"调用大模型2: {messages}")
            # 调用LLM获取完整响应
            response =  llm_with_tools.invoke({"messages": messages})
            logger.info(f"LLM Response22: {response}")
            
            return {
                "messages": [response]
            }
    
    def route_after_llm(self, state) -> Literal["__end__", "human_review_node"]:
        if len(state["messages"][-1].tool_calls) == 0:
            logger.info("无tool_calls,直接结束")
            return END
        else:
            logger.info("有tool_calls,进入human_review_node")
            return "human_review_node"
    
  
    def human_review_node(self,state) -> Command[Literal["query_company", "run_tool"]]:
        last_message = state["messages"][-1]
        tool_call = last_message.tool_calls[-1]

        # this is the value we'll be providing via Command(resume=<human_review>)
        human_review = interrupt(
            {
                "message": "准备调用工具是否确认继续? yes / no",
                # Surface tool calls for review
                "tool_call": tool_call,
            }
        )
        logger.info(f"human_review: {human_review}")
        review_action = human_review["action"]
        review_data = human_review.get("data")
        if review_action == "yes":
            return Command(goto="run_tool") 
        elif review_action == "update":
            review_data = {"company_name": "特斯拉"}
            updated_message = {
                "role": "ai",
                "content": last_message.content,
                "tool_calls": [
                    {
                        "id": tool_call["id"],
                        "name": tool_call["name"],
                        "args": review_data,
                    }
                ],
                # This is important - this needs to be the same as the message you replacing!
                # Otherwise, it will show up as a separate message
                "id": last_message.id,
            }
            return Command(goto="run_tool", update={"messages": [updated_message]})
        else:
            # 以保留消息历史中的正确顺序
            # （带用有工具调用的AI消息需要后续工具调消息）
            tool_message = {
                "role": "human",
                # 这是我们的自然语言反馈。
                "content": review_data,
                
            }
            return Command(goto="query_company", update={"messages": [tool_message]})

    def run_tool(self,state):
        new_messages = []
        tools = {"query_company_info": query_company_info}
        tool_calls = state["messages"][-1].tool_calls
        logger.info(f"run_tool: {tool_calls}")
        for tool_call in tool_calls:
            tool = tools[tool_call["name"]]
            result = tool.invoke(tool_call["args"])
            new_messages.append(
                {
                    "role": "tool",
                    "name": tool_call["name"],
                    "content": result,
                    "tool_call_id": tool_call["id"],
                }
            )
        return {"messages": new_messages} 
    def build_langgraph_agent(self, agent_id: str, tools: List[BaseTool], model_config: ModelConfig):
        """实现自定义流程图"""
        # 这里可以调用父类的公共方法
        # 定义图
        workflow = StateGraph(AgentState)
        
        
        # 添加节点到图中
        # 一、根据用户输入，查询企业信息
        # workflow.add_node("query_company", self.create_agent_node(
        #         agent_id, 
        #         "company_query",
        #         tools, 
        #         model_config, 
        #         run_config={"run_name": "aaaaaa", "tags": ["company_query"]}
        # ))

        def query_company_node(state: AgentState):
            # 通过闭包捕获外层的 agent_id 与节点名
            return self.call_llm(state, tools, model_config, agent_id=agent_id, node_name="query_company")

        workflow.add_node("query_company", query_company_node)
        workflow.add_node("run_tool", self.run_tool)
        workflow.add_node("human_review_node", self.human_review_node) 
        
        
        # 设置入口点
        workflow.add_edge(START, "query_company")
        
        # 添加条件边: query_company节点之后的决策
        workflow.add_conditional_edges(
            "query_company",
            self.route_after_llm
        )

        # 添加边：工具 -> Agent
        workflow.add_edge("run_tool", "query_company")
        
        # 编译图
        memory = MemorySaver()
        graph = workflow.compile(checkpointer=memory)
        
        return graph