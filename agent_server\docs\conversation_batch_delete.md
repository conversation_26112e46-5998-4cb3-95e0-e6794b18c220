# 会话批量删除功能

## 概述

新增了会话批量删除功能，允许用户一次性删除多个会话，提高操作效率。

## API接口

### 批量删除会话

**接口路径**: `POST /conversation/batch-delete`

**需要认证**: 是

**请求体**:
```json
{
  "conversation_ids": [
    "conversation_id_1",
    "conversation_id_2",
    "conversation_id_3"
  ]
}
```

**响应格式**:
```json
{
  "code": "200",
  "message": "成功删除 2 个会话，1 个会话未找到或已删除",
  "data": {
    "deleted_count": 2,
    "deleted_ids": ["conversation_id_1", "conversation_id_2"],
    "not_found_ids": ["conversation_id_3"],
    "total_requested": 3
  },
  "pagination": null
}
```

## 功能特点

- ✅ **批量操作**: 支持一次删除多个会话
- ✅ **安全性**: 只能删除属于当前用户的会话
- ✅ **软删除**: 使用软删除机制，数据可恢复
- ✅ **详细反馈**: 返回删除成功和失败的详细信息
- ✅ **性能优化**: 使用批量SQL更新提高性能
- ✅ **限制保护**: 单次最多删除100个会话

## 错误处理

| 错误情况 | HTTP状态码 | 错误信息 |
|---------|-----------|---------|
| 空列表 | 400 | 会话ID列表不能为空 |
| 超过限制 | 400 | 单次批量删除不能超过100个会话 |
| 数据库错误 | 500 | 批量删除会话失败: {具体错误} |

## 使用示例

### cURL 示例

```bash
curl -X POST "http://localhost:8000/conversation/batch-delete" \
     -H "Content-Type: application/json" \
     -H "Authorization: Bearer YOUR_TOKEN" \
     -d '{
       "conversation_ids": [
         "conv_123",
         "conv_456", 
         "conv_789"
       ]
     }'
```

### JavaScript 示例

```javascript
const response = await fetch('/conversation/batch-delete', {
  method: 'POST',
  headers: {
    'Content-Type': 'application/json',
    'Authorization': 'Bearer YOUR_TOKEN'
  },
  body: JSON.stringify({
    conversation_ids: ['conv_123', 'conv_456', 'conv_789']
  })
});

const result = await response.json();
console.log(`删除了 ${result.data.deleted_count} 个会话`);
```

## 实现细节

### 数据库层 (CRUD)

- **方法**: `conversation_curd.batch_soft_delete()`
- **验证**: 检查会话所有权和删除状态
- **操作**: 批量更新 `is_deleted=True` 和 `updated_at`
- **返回**: 详细的操作结果统计

### API层

- **认证**: 使用 `check_user` 依赖注入
- **验证**: 检查请求数据格式和业务规则
- **处理**: 调用CRUD方法执行删除
- **响应**: 统一的响应格式

### 数据模型

```python
class ConversationBatchDelete(SQLModel):
    """批量删除会话请求模型"""
    conversation_ids: List[str] = Field(description="要删除的会话ID列表")
```

## 测试

项目包含完整的测试用例：

- `tests/test_conversation_batch_delete.py` - 数据库层测试
- `tests/test_conversation_batch_delete_api.py` - API层测试

运行测试：
```bash
python -m unittest tests.test_conversation_batch_delete_api -v
```

## 注意事项

1. **权限控制**: 用户只能删除自己的会话
2. **软删除**: 删除的会话可以通过管理员功能恢复
3. **性能考虑**: 建议单次删除不超过100个会话
4. **事务安全**: 所有操作在数据库事务中执行，保证一致性
