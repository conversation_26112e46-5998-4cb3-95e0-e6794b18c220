

import importlib
import inspect
import pkgutil
from typing import Dict, List, Any, Callable
from fastapi import APIRouter
import yaml
from dataclasses import dataclass, field
from agent_server.core.config.app_logger import logger

# 路由注册系统,提供装饰器和配置文件两种方式来动态注册路由模块
# @Author: xiangjh

@dataclass
class RouterConfig:
    """路由配置信息"""
    module_path: str  # 模块路径，
    router_name: str = "router"  # 路由对象名称
    prefix: str = ""  # 路由前缀
    tags: List[str] = field(default_factory=list)  # 路由标签
    dependencies: List[Any] = field(default_factory=list)  # 依赖项
    enabled: bool = True  # 是否启用
    description: str = ""  # 描述信息
    priority: int = 100  # 优先级，数字越小优先级越高


class RouterRegistry:
    """路由注册器"""
    
    def __init__(self):
        self._registered_routers: Dict[str, RouterConfig] = {}
        self._decorator_routers: Dict[str, RouterConfig] = {}
        
    def register_router(self, config: RouterConfig) -> None:
        """注册路由配置"""
        self._registered_routers[config.module_path] = config
        logger.debug(f"已加载路由配置: {config.module_path}")
    
    def get_all_routers(self) -> Dict[str, RouterConfig]:
        """获取所有注册的路由配置"""
        # 合并装饰器注册和手动注册的路由，手动注册的优先级更高
        all_routers = self._decorator_routers.copy()
        all_routers.update(self._registered_routers)
        return all_routers
    
    def discover_routers(self, scan_packages: List[str] = None) -> None:
        """发现并注册使用装饰器标记的路由"""
        if scan_packages is None:
            scan_packages = ["sender.api", "plugins"]
            
        for package_name in scan_packages:
            try:
                self._scan_package(package_name)
            except ImportError as e:
                logger.warning(f"扫描包 {package_name} 失败: {e}")
                continue
    
    def _scan_package(self, package_name: str) -> None:
        """扫描指定包中的路由装饰器"""
        try:
            package = importlib.import_module(package_name)
            package_path = package.__path__
        except (ImportError, AttributeError):
            logger.warning(f"无法导入包: {package_name}")
            return
            
        for importer, modname, ispkg in pkgutil.walk_packages(
            package_path, 
            prefix=f"{package_name}."
        ):
            try:
                module = importlib.import_module(modname)
                self._scan_module(module)
            except Exception as e:
                logger.debug(f"扫描模块 {modname} 失败: {e}")
                continue
    
    def _scan_module(self, module) -> None:
        """扫描模块中的路由装饰器标记"""
        if hasattr(module, '__router_config__'):
            config = module.__router_config__
            self._decorator_routers[config.module_path] = config
            #logger.debug(f"发现装饰器注册的路由: {config.module_path}")


# 全局路由注册器实例
router_registry = RouterRegistry()

def router_register(
    prefix: str = "",
    tags: List[str] = None,
    dependencies: List[Any] = None,
    enabled: bool = True,
    description: str = "",
    priority: int = 100,
    router_name: str = "router"
):
    """
    路由注册装饰器 - 用于模块级别的注册

    Args:
        prefix: 路由前缀
        tags: 路由标签
        dependencies: 依赖项
        enabled: 是否启用
        description: 描述信息
        priority: 优先级
        router_name: 路由对象名称

    Usage:
        # 在模块顶部调用
        router_register(prefix="/rags", tags=["RAGs知识库"])

        # 然后定义路由
        router = APIRouter(tags=["RAGs知识库"])
    """
    if tags is None:
        tags = []
    if dependencies is None:
        dependencies = []

    # 获取调用者的模块
    frame = inspect.currentframe().f_back
    module = inspect.getmodule(frame)
    module_path = module.__name__

    # 创建路由配置
    config = RouterConfig(
        module_path=module_path,
        router_name=router_name,
        prefix=prefix,
        tags=tags,
        dependencies=dependencies,
        enabled=enabled,
        description=description,
        priority=priority
    )

    # 将配置添加到模块中
    module.__router_config__ = config
    logger.debug(f"注册路由模块: {module_path}")


# 保留装饰器版本用于函数装饰
def router_decorator(
    prefix: str = "",
    tags: List[str] = None,
    dependencies: List[Any] = None,
    enabled: bool = True,
    description: str = "",
    priority: int = 100,
    router_name: str = "router"
) -> Callable:
    """
    路由装饰器版本 - 用于装饰函数

    Usage:
        @router_decorator(prefix="/rags", tags=["RAGs知识库"])
        def create_router():
            return APIRouter()

        router = create_router()
    """
    if tags is None:
        tags = []
    if dependencies is None:
        dependencies = []

    def decorator(func):
        # 获取调用装饰器的模块
        frame = inspect.currentframe().f_back
        module = inspect.getmodule(frame)
        module_path = module.__name__

        # 创建路由配置
        config = RouterConfig(
            module_path=module_path,
            router_name=router_name,
            prefix=prefix,
            tags=tags,
            dependencies=dependencies,
            enabled=enabled,
            description=description,
            priority=priority
        )

        # 将配置添加到模块中
        module.__router_config__ = config

        logger.debug(f"装饰器注册路由: {module_path}")
        return func

    return decorator


def load_routers_from_config(config_path: str = "agent_server/config/application.yml") -> None:
    """从配置文件加载路由注册信息"""
    try:
        with open(config_path, 'r', encoding='utf-8') as f:
            config_data = yaml.safe_load(f)
        
        routers_config = config_data.get('routers', {})
        
        # 加载手动注册的路由
        manual_routers = routers_config.get('manual', [])
        for router_config in manual_routers:
            config = RouterConfig(**router_config)
            logger.warning(f"开始加载手动注册的路由 {config.module_path}...")
            router_registry.register_router(config)
            
        # 配置自动发现的包
        auto_discovery = routers_config.get('auto_discovery', {})
        if auto_discovery.get('enabled', True):
            scan_packages = auto_discovery.get('packages', ["rags", "plugins"])
            logger.warning(f"开始加载需要扫描的包 {scan_packages}...")
            router_registry.discover_routers(scan_packages)
            
        logger.info("所有路由配置加载完成")
        
    except FileNotFoundError:
        logger.warning(f"配置文件 {config_path} 不存在，使用默认配置")
        # 使用默认配置进行自动发现
        router_registry.discover_routers()
    except Exception as e:
        logger.error(f"加载路由配置失败: {e}")
        # 发生错误时仍尝试自动发现
        router_registry.discover_routers()


def apply_routers_to_app(main_router: APIRouter, public_router: APIRouter = None) -> None:
    """将注册的路由应用到主应用中"""
    all_routers = router_registry.get_all_routers()
    
    # 按优先级排序
    sorted_routers = sorted(all_routers.values(), key=lambda x: x.priority)
    
    for config in sorted_routers:
        if not config.enabled:
            logger.info(f"跳过禁用的路由: {config.module_path}")
            continue
            
        try:
            # 动态导入模块
            module = importlib.import_module(config.module_path)
            router_obj = getattr(module, config.router_name)
            
            if not isinstance(router_obj, APIRouter):
                logger.warning(f"模块 {config.module_path} 中的 {config.router_name} 不是 APIRouter 实例")
                continue
            
            target_router = main_router
            if hasattr(module, '__router_type__') and module.__router_type__ == 'public':
                target_router = public_router or main_router
            
            # 注册路由
            include_kwargs = {}
            if config.prefix:
                include_kwargs['prefix'] = config.prefix
            if config.tags:
                include_kwargs['tags'] = config.tags
            if config.dependencies:
                include_kwargs['dependencies'] = config.dependencies
                
            target_router.include_router(router_obj, **include_kwargs)
            
            logger.info(f"成功加载路由模块: {config.module_path} (前缀: {config.prefix})")
            
        except ImportError as e:
            logger.warning(f"导入路由模块失败: {config.module_path} - {e}")
        except AttributeError as e:
            logger.warning(f"路由模块中未找到指定的路由对象: {config.module_path}.{config.router_name} - {e}")
        except Exception as e:
            logger.error(f"加载路由模块时出错: {config.module_path} - {e}")
