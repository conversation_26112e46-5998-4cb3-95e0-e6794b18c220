# 启动服务 确保 PYTHONPATH 包含项目根目录或 src 目录
# set PYTHONPATH=d:\projects\agent-server\src
# uvicorn src.core.main:app --reload --port 8000 --host 0.0.0.0

# 使用 curl 或 Postman 发送含有 Authorization 头的请求：
# curl -H "Authorization: Bearer CF1f-M0MRKFoqNJmYJyO8zjoxfQ" http://localhost:8000/admin/data
# <AUTHOR> xiangjh
# pylint: disable=wrong-import-position

import uvicorn
from dotenv import load_dotenv

load_dotenv()

from agent_server.playground.agents.test.main import TestAgent
from agent_server.core.server.app import create_app
from agent_server.core.api.router import (
    router_private,
    router_public,
)
from agent_server.gateway.main import router as gateway_router

app = create_app(routers=[router_private, router_public, gateway_router])

test_agent = TestAgent(app)

if __name__ == "__main__":
    uvicorn.run(app, host="0.0.0.0", port=8000)
