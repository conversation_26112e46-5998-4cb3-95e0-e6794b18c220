from typing import List
from sqlmodel import select

from agent_server.core.services.database import db_manager
from agent_server.core.services.database.crud.base import CRUDBase
from agent_server.core.services.database.schemas.model_management import ModelManagementTable, ModelManagementCreate, ModelManagementUpdate


# ModelManagement 增删改查操作
# <AUTHOR> silence_w
class CRUDModelManagement(CRUDBase[ModelManagementTable, ModelManagementCreate, ModelManagementUpdate]):
    """ModelManagement CRUD操作实现"""
    
    
    async def query_models(self,*,user=None) -> List[ModelManagementTable]:
        """获取所有ModelManagement列表"""
        async with db_manager.session() as db:
            result = await db.execute(select(ModelManagementTable))
            return result.scalars().all()

   


model_management_crud = CRUDModelManagement(ModelManagementTable)