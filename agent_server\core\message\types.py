from typing import TypedDict

from dataclasses import dataclass
from enum import Enum

from pydantic import BaseModel

@dataclass
class Chunk:
    """
    消息块
    """
    event_id: int
    event_type: int
    package_id: int
    package_type: int
    chunk_id: str
    is_last: bool
    data: str

# status 枚举
class MessagePackageStatus(int, Enum):
    """
    消息状态
    """
    LOADING = 0
    FINISHED = 1
    ERROR = 2

class MessagePackage(BaseModel):
    """
    消息包
    """
    package_id: int
    package_type: int
    status: MessagePackageStatus
    data: str

class MessageType(str, Enum):
    """
    消息角色
    """
    HUMAN = "human"
    AI = "ai"
    SYSTEM = "system"