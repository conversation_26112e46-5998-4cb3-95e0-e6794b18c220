"""add_user_uploaded_files_table

Revision ID: 260dd70ca243
Revises: 558b1c024a94
Create Date: 2025-09-11 09:19:28.766091

"""
from typing import Sequence, Union

from alembic import op
import sqlalchemy as sa
from sqlalchemy.dialects import postgresql
import sqlmodel

# revision identifiers, used by Alembic.
revision: str = '260dd70ca243'
down_revision: Union[str, None] = '558b1c024a94'
branch_labels: Union[str, Sequence[str], None] = None
depends_on: Union[str, Sequence[str], None] = None


def upgrade() -> None:
    """Upgrade schema."""
    # ### commands auto generated by Alembic - please adjust! ###
    op.create_table('user_uploaded_files',
    sa.Column('id', sqlmodel.sql.sqltypes.AutoString(), nullable=False),
    sa.Column('user_id', sa.Integer(), nullable=False),
    sa.Column('file_name', sqlmodel.sql.sqltypes.AutoString(), nullable=False),
    sa.Column('file_hash', sqlmodel.sql.sqltypes.AutoString(), nullable=False),
    sa.Column('file_size', sa.Integer(), nullable=False),
    sa.Column('file_type', sqlmodel.sql.sqltypes.AutoString(length=50), nullable=False),
    sa.Column('ragflow_doc_id', sqlmodel.sql.sqltypes.AutoString(), nullable=False),
    sa.Column('status', sa.Enum('UPLOADING', 'PARSING', 'READY', 'ERROR', name='filestatus'), nullable=False),
    sa.Column('error_message', sqlmodel.sql.sqltypes.AutoString(), nullable=True),
    sa.Column('created_at', sa.DateTime(timezone=True), nullable=True),
    sa.Column('updated_at', sa.DateTime(timezone=True), nullable=True),
    sa.CheckConstraint('file_size > 0', name='check_positive_file_size'),
    sa.PrimaryKeyConstraint('id')
    )
    op.create_index('idx_file_hash_user', 'user_uploaded_files', ['file_hash', 'user_id'], unique=False)
    op.create_index('idx_ragflow_doc', 'user_uploaded_files', ['ragflow_doc_id'], unique=False)
    op.create_index('idx_user_files', 'user_uploaded_files', ['user_id', 'status'], unique=False)
    op.create_index(op.f('ix_user_uploaded_files_file_hash'), 'user_uploaded_files', ['file_hash'], unique=False)
    op.create_index(op.f('ix_user_uploaded_files_ragflow_doc_id'), 'user_uploaded_files', ['ragflow_doc_id'], unique=False)
    op.create_index(op.f('ix_user_uploaded_files_user_id'), 'user_uploaded_files', ['user_id'], unique=False)
    op.drop_index('idx_ai_document_chunk_chunk_index', table_name='ai_document_chunk')
    op.drop_index('idx_ai_document_chunk_document_id', table_name='ai_document_chunk')
    op.drop_index('idx_ai_document_chunk_knowledge_base_id', table_name='ai_document_chunk')
    op.drop_index('idx_ai_document_chunk_recall_count', table_name='ai_document_chunk')
    op.drop_index('idx_ai_document_chunk_vector_id', table_name='ai_document_chunk')
    op.drop_table('ai_document_chunk')
    op.drop_table('ai_knowledge_base')
    op.drop_index('idx_ai_document_create_time', table_name='ai_document')
    op.drop_index('idx_ai_document_knowledge_base_id', table_name='ai_document')
    op.drop_index('idx_ai_document_status', table_name='ai_document')
    op.drop_table('ai_document')
    # ### end Alembic commands ###


def downgrade() -> None:
    """Downgrade schema."""
    # ### commands auto generated by Alembic - please adjust! ###
    op.create_table('ai_document',
    sa.Column('id', sa.INTEGER(), autoincrement=True, nullable=False),
    sa.Column('knowledge_base_id', sa.INTEGER(), autoincrement=False, nullable=False, comment='所属知识库ID'),
    sa.Column('title', sa.VARCHAR(length=500), autoincrement=False, nullable=False, comment='文档标题'),
    sa.Column('content', sa.TEXT(), autoincrement=False, nullable=True, comment='文档内容'),
    sa.Column('file_name', sa.VARCHAR(length=255), autoincrement=False, nullable=True, comment='原始文件名'),
    sa.Column('file_type', sa.VARCHAR(length=50), autoincrement=False, nullable=True, comment='文件类型'),
    sa.Column('file_size', sa.INTEGER(), autoincrement=False, nullable=True, comment='文件大小(字节)'),
    sa.Column('status', sa.VARCHAR(length=50), server_default=sa.text("'pending'::character varying"), autoincrement=False, nullable=True, comment='处理状态'),
    sa.Column('error_message', sa.TEXT(), autoincrement=False, nullable=True, comment='错误信息'),
    sa.Column('chunk_count', sa.INTEGER(), server_default=sa.text('0'), autoincrement=False, nullable=True, comment='分段数量'),
    sa.Column('character_count', sa.INTEGER(), server_default=sa.text('0'), autoincrement=False, nullable=True, comment='字符数量'),
    sa.Column('doc_metadata', sa.TEXT(), autoincrement=False, nullable=True, comment='文档元数据(JSON格式)'),
    sa.Column('is_deleted', sa.BOOLEAN(), server_default=sa.text('false'), autoincrement=False, nullable=True, comment='是否删除'),
    sa.Column('create_time', postgresql.TIMESTAMP(), autoincrement=False, nullable=True, comment='创建时间'),
    sa.Column('update_time', postgresql.TIMESTAMP(), autoincrement=False, nullable=True, comment='更新时间'),
    sa.Column('processed_at', postgresql.TIMESTAMP(), autoincrement=False, nullable=True, comment='处理完成时间'),
    sa.Column('create_by', sa.VARCHAR(length=255), autoincrement=False, nullable=False, comment='创建者'),
    sa.Column('update_by', sa.VARCHAR(length=255), autoincrement=False, nullable=True, comment='修改者'),
    sa.Column('chunk_size', sa.INTEGER(), autoincrement=False, nullable=True),
    sa.Column('chunk_overlap', sa.INTEGER(), autoincrement=False, nullable=True),
    sa.Column('separators', sa.TEXT(), autoincrement=False, nullable=True),
    sa.PrimaryKeyConstraint('id', name='ai_document_pkey')
    )
    op.create_index('idx_ai_document_status', 'ai_document', ['status'], unique=False)
    op.create_index('idx_ai_document_knowledge_base_id', 'ai_document', ['knowledge_base_id'], unique=False)
    op.create_index('idx_ai_document_create_time', 'ai_document', ['create_time'], unique=False)
    op.create_table('ai_knowledge_base',
    sa.Column('id', sa.INTEGER(), autoincrement=True, nullable=False),
    sa.Column('name', sa.VARCHAR(length=255), autoincrement=False, nullable=False, comment='知识库名称'),
    sa.Column('description', sa.TEXT(), autoincrement=False, nullable=True, comment='知识库描述'),
    sa.Column('collection_name', sa.VARCHAR(length=255), autoincrement=False, nullable=False, comment='向量数据库集合名称'),
    sa.Column('embedding_model', sa.VARCHAR(length=255), autoincrement=False, nullable=False, comment='使用的嵌入模型'),
    sa.Column('dimension', sa.INTEGER(), autoincrement=False, nullable=False, comment='向量维度'),
    sa.Column('chunk_size', sa.INTEGER(), server_default=sa.text('1024'), autoincrement=False, nullable=True, comment='分段最大长度'),
    sa.Column('chunk_overlap', sa.INTEGER(), server_default=sa.text('200'), autoincrement=False, nullable=True, comment='分段重叠长度'),
    sa.Column('separators', sa.TEXT(), server_default=sa.text('\'[\\"\\\\n\\\\n\\", \\"\\\\n\\"]\'::text'), autoincrement=False, nullable=True, comment='分段标识符(JSON格式)'),
    sa.Column('document_count', sa.INTEGER(), server_default=sa.text('0'), autoincrement=False, nullable=True, comment='文档数量'),
    sa.Column('chunk_count', sa.INTEGER(), server_default=sa.text('0'), autoincrement=False, nullable=True, comment='分段数量'),
    sa.Column('metadata_schema', sa.TEXT(), autoincrement=False, nullable=True, comment='元数据模式(JSON格式)'),
    sa.Column('status', sa.VARCHAR(length=50), server_default=sa.text("'active'::character varying"), autoincrement=False, nullable=True, comment='知识库状态'),
    sa.Column('is_deleted', sa.BOOLEAN(), server_default=sa.text('false'), autoincrement=False, nullable=True, comment='是否删除'),
    sa.Column('create_time', postgresql.TIMESTAMP(timezone=True), server_default=sa.text('CURRENT_TIMESTAMP'), autoincrement=False, nullable=True, comment='创建时间'),
    sa.Column('update_time', postgresql.TIMESTAMP(timezone=True), server_default=sa.text('CURRENT_TIMESTAMP'), autoincrement=False, nullable=True, comment='更新时间'),
    sa.Column('create_by', sa.VARCHAR(length=255), autoincrement=False, nullable=False, comment='创建者'),
    sa.Column('update_by', sa.VARCHAR(length=255), autoincrement=False, nullable=True, comment='修改者'),
    sa.PrimaryKeyConstraint('id', name='ai_knowledge_base_pkey'),
    sa.UniqueConstraint('collection_name', name='ai_knowledge_base_collection_name_key', postgresql_include=[], postgresql_nulls_not_distinct=False),
    sa.UniqueConstraint('name', name='ai_knowledge_base_name_key', postgresql_include=[], postgresql_nulls_not_distinct=False)
    )
    op.create_table('ai_document_chunk',
    sa.Column('id', sa.INTEGER(), autoincrement=True, nullable=False),
    sa.Column('document_id', sa.INTEGER(), autoincrement=False, nullable=False, comment='所属文档ID'),
    sa.Column('knowledge_base_id', sa.INTEGER(), autoincrement=False, nullable=False, comment='所属知识库ID'),
    sa.Column('text', sa.TEXT(), autoincrement=False, nullable=False, comment='分段文本'),
    sa.Column('chunk_index', sa.INTEGER(), autoincrement=False, nullable=False, comment='分段序号'),
    sa.Column('start_index', sa.INTEGER(), autoincrement=False, nullable=False, comment='在原文档中的起始位置'),
    sa.Column('end_index', sa.INTEGER(), autoincrement=False, nullable=False, comment='在原文档中的结束位置'),
    sa.Column('vector_id', sa.VARCHAR(length=255), autoincrement=False, nullable=True, comment='向量数据库中的ID'),
    sa.Column('embedding_model', sa.VARCHAR(length=255), autoincrement=False, nullable=True, comment='嵌入模型'),
    sa.Column('character_count', sa.INTEGER(), server_default=sa.text('0'), autoincrement=False, nullable=True, comment='字符数量'),
    sa.Column('recall_count', sa.INTEGER(), server_default=sa.text('0'), autoincrement=False, nullable=True, comment='召回次数'),
    sa.Column('chunk_metadata', sa.TEXT(), autoincrement=False, nullable=True, comment='分段元数据(JSON格式)'),
    sa.Column('keywords', sa.TEXT(), autoincrement=False, nullable=True, comment='关键词(JSON格式)'),
    sa.Column('is_deleted', sa.BOOLEAN(), server_default=sa.text('false'), autoincrement=False, nullable=True, comment='是否删除'),
    sa.Column('create_time', postgresql.TIMESTAMP(), autoincrement=False, nullable=True, comment='创建时间'),
    sa.Column('update_time', postgresql.TIMESTAMP(), autoincrement=False, nullable=True, comment='更新时间'),
    sa.Column('last_recalled_at', postgresql.TIMESTAMP(), autoincrement=False, nullable=True, comment='最后召回时间'),
    sa.Column('create_by', sa.VARCHAR(length=255), autoincrement=False, nullable=False, comment='创建者'),
    sa.Column('update_by', sa.VARCHAR(length=255), autoincrement=False, nullable=True, comment='修改者'),
    sa.PrimaryKeyConstraint('id', name='ai_document_chunk_pkey'),
    sa.UniqueConstraint('document_id', 'chunk_index', name='uk_ai_document_chunk_doc_index', postgresql_include=[], postgresql_nulls_not_distinct=False)
    )
    op.create_index('idx_ai_document_chunk_vector_id', 'ai_document_chunk', ['vector_id'], unique=False)
    op.create_index('idx_ai_document_chunk_recall_count', 'ai_document_chunk', ['recall_count'], unique=False)
    op.create_index('idx_ai_document_chunk_knowledge_base_id', 'ai_document_chunk', ['knowledge_base_id'], unique=False)
    op.create_index('idx_ai_document_chunk_document_id', 'ai_document_chunk', ['document_id'], unique=False)
    op.create_index('idx_ai_document_chunk_chunk_index', 'ai_document_chunk', ['chunk_index'], unique=False)
    op.drop_index(op.f('ix_user_uploaded_files_user_id'), table_name='user_uploaded_files')
    op.drop_index(op.f('ix_user_uploaded_files_ragflow_doc_id'), table_name='user_uploaded_files')
    op.drop_index(op.f('ix_user_uploaded_files_file_hash'), table_name='user_uploaded_files')
    op.drop_index('idx_user_files', table_name='user_uploaded_files')
    op.drop_index('idx_ragflow_doc', table_name='user_uploaded_files')
    op.drop_index('idx_file_hash_user', table_name='user_uploaded_files')
    op.drop_table('user_uploaded_files')
    # ### end Alembic commands ###
