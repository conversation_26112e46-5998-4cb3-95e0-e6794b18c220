"""update prompt_tempate group and preview

Revision ID: 4702be57162c
Revises: f44b52ea782b
Create Date: 2025-07-14 18:03:48.068456

"""
from typing import Sequence, Union

from alembic import op
import sqlalchemy as sa

import sqlmodel

# revision identifiers, used by Alembic.
revision: str = '4702be57162c'
down_revision: Union[str, None] = 'f44b52ea782b'
branch_labels: Union[str, Sequence[str], None] = None
depends_on: Union[str, Sequence[str], None] = None


def upgrade() -> None:
    """Upgrade schema."""
    # ### commands auto generated by Alembic - please adjust! ###
    op.alter_column('ai_agent', 'base_url',
               existing_type=sa.VARCHAR(length=255),
               nullable=False)
    op.alter_column('ai_agent', 'api_key',
               existing_type=sa.VARCHAR(length=255),
               nullable=False)
    op.alter_column('ai_agent', 'router_url',
               existing_type=sa.VARCHAR(length=255),
               nullable=False)
    op.alter_column('ai_agent', 'agent_desc',
               existing_type=sa.TEXT(),
               type_=sqlmodel.sql.sqltypes.AutoString(),
               existing_nullable=True)
    op.drop_index('idx_message_content_item_data', table_name='message', postgresql_using='gin')
    op.drop_index('idx_message_content_tsvector', table_name='message', postgresql_using='gin')
    op.alter_column('prompt_template', 'group',
               existing_type=sa.VARCHAR(),
               nullable=True)
    op.alter_column('prompt_template', 'preview',
               existing_type=sa.VARCHAR(),
               nullable=True)
    op.drop_index('ix_prompt_template_group', table_name='prompt_template')
    op.drop_index('ix_prompt_template_order', table_name='prompt_template')
    op.drop_index('ix_prompt_template_type', table_name='prompt_template')
    op.drop_index('ix_prompt_template_type_group', table_name='prompt_template')
    # ### end Alembic commands ###


def downgrade() -> None:
    """Downgrade schema."""
    # ### commands auto generated by Alembic - please adjust! ###
    op.create_index('ix_prompt_template_type_group', 'prompt_template', ['type', 'group'], unique=False)
    op.create_index('ix_prompt_template_type', 'prompt_template', ['type'], unique=False)
    op.create_index('ix_prompt_template_order', 'prompt_template', ['order'], unique=False)
    op.create_index('ix_prompt_template_group', 'prompt_template', ['group'], unique=False)
    op.alter_column('prompt_template', 'preview',
               existing_type=sa.VARCHAR(),
               nullable=False)
    op.alter_column('prompt_template', 'group',
               existing_type=sa.VARCHAR(),
               nullable=False)
    op.create_index('idx_message_content_tsvector', 'message', ['content_tsvector'], unique=False, postgresql_using='gin')
    op.create_index('idx_message_content_item_data', 'message', [sa.literal_column("(content -> 'data'::text)")], unique=False, postgresql_using='gin')
    op.alter_column('ai_agent', 'agent_desc',
               existing_type=sqlmodel.sql.sqltypes.AutoString(),
               type_=sa.TEXT(),
               existing_nullable=True)
    op.alter_column('ai_agent', 'router_url',
               existing_type=sa.VARCHAR(length=255),
               nullable=True)
    op.alter_column('ai_agent', 'api_key',
               existing_type=sa.VARCHAR(length=255),
               nullable=True)
    op.alter_column('ai_agent', 'base_url',
               existing_type=sa.VARCHAR(length=255),
               nullable=True)
    # ### end Alembic commands ###
