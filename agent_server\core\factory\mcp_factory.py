from typing import List
from agent_server.mcpclient.mcp_client import <PERSON><PERSON>p<PERSON><PERSON>
from agent_server.core.config.app_logger import logger
from agent_server.core.config.app_config import config
import asyncio

# Mcp工厂类类
# 实现多协议 MCP 客户端的动态初始化与管理，主要功能：
#    - 配置驱动的客户端初始化
#    - 多协议支持（标准IO/SSE）
#    - 连接生命周期管理
#    - 异常熔断
# @Author: xiangjh

class McpFactory:
    def __init__(self):
        self._clients = []
        self._initialized = False
        self._lock = asyncio.Lock()

    async def create_mcp_clients(self) -> List[McpClient]:
        """MCP 客户端集群构建器"""
        # async with self._lock:
        #     self._initialized = False
        #     if not self._initialized:
        #         await self._init_clients()
        #     return self._clients.copy()
        async with self._lock:
            if self._clients:
                await self.close_clients()
            
            self._initialized = False
            await self._init_clients()
            return self._clients.copy()

    async def _init_clients(self):
        """初始化MCP客户端连接池"""
        servers = config.mcp_servers
        clients = []
        
        for server in servers:
            if not server.enabled:
                logger.info(f"MCPClient {server.key} 未启用")
                continue

            try:
                client = McpClient()
                if server.type == "stdio":
                    await client.connect_to_server(server.url)
                elif server.type == "sse":
                    await client.connect_to_server_url(server.url)
                
                clients.append(client)
                logger.info(f"MCPClient {server.key} 初始化成功")
            except Exception as e:
                logger.error(f"MCPClient {server.key} 初始化失败: {str(e)}", exc_info=True)
                continue
        
        self._clients = clients
        self._initialized = True

    async def close_clients(self):
        """关闭所有MCP客户端连接"""
        async with self._lock:
            if self._clients:
                for client in self._clients:
                    await client.disconnect()

                #await asyncio.gather(*[client.disconnect() for client in self._clients])
                self._clients.clear()
            self._initialized = False
