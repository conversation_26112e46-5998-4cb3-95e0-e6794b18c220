# PyPI configuration file template
#
# 使用说明:
# 1. 复制此文件到用户家目录: cp .pypirc.template ~/.pypirc
# 2. 编辑 ~/.pypirc 文件，填入真实的凭据
# 3. 设置文件权限: chmod 600 ~/.pypirc
# 4. 使用 twine 上传: twine upload dist/*

[distutils]
index-servers =
    pypi
    testpypi
    private

# 官方 PyPI (生产环境)
# 地址: https://pypi.org/
# 上传地址: https://upload.pypi.org/legacy/ (这是官方的上传接口)
[pypi]
repository = https://upload.pypi.org/legacy/
username = __token__
password = pypi-your-api-token-here

# 测试 PyPI (测试环境)
# 地址: https://test.pypi.org/
# 用于测试包发布，避免污染正式环境
[testpypi]
repository = https://test.pypi.org/legacy/
username = __token__
password = pypi-your-test-api-token-here

# 私有 PyPI (企业内部)
# 替换为你的私有仓库地址
# 常见私有仓库: Nexus, Artifactory, DevPI 等
[private]
repository = https://your-private-pypi.com/simple/
username = your-username
password = your-password

# 示例: 使用阿里云私有仓库
# [aliyun]
# repository = https://packages.aliyun.com/pypi/simple/
# username = your-aliyun-username
# password = your-aliyun-password

# 示例: 使用腾讯云私有仓库
# [tencent]
# repository = https://mirrors.cloud.tencent.com/pypi/simple/
# username = your-tencent-username
# password = your-tencent-password
