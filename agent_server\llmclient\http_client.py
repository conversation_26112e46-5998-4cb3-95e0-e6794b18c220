import httpx
from typing import Optional
from agent_server.core.config.app_logger import logger

# HttpClient 客户端封装类，用于封装http的基础调用
# @Author: xiangjh
class HttpClient:
    def __init__(
        self,
        base_url: str = None ,
        agent_code: Optional[str] = None
    ):
        self.base_url = base_url
        self.agent_code = agent_code

    def _resolve_base_url(self) -> str:
        """解析最终base_url逻辑"""
        
        if self.base_url:
            return self.base_url
            
        if self.agent_code:
            # 从配置中获取agent对应的base_url
            from agent_server.core.factory.agent_factory import AgentFactory
            agent = AgentFactory.get_agent(self.agent_code)
            logger.debug(f">>>agent url: {agent.base_url}")
            if agent and agent.base_url:
                return agent.base_url
                
        raise ValueError("无法解析服务地址：base_url和agent_code均为空")
    
    async def http_query(
        self,
        headers: dict = None,  
        payload: dict = None   
    ) -> str:
        """执行http查询"""
        _url = self._resolve_base_url()
        
        # 设置默认值
        _headers = headers or {"Content-Type": "application/json"}
        _payload = payload or {}

        try:
            async with httpx.AsyncClient(timeout=120) as client:
                response = await client.post(_url, json=_payload, headers=_headers)
                response.raise_for_status()
                logger.debug(f"http响应: {response.json()}")
                return response.json()
                
        except Exception as e:
            logger.error(f"http请求失败: {str(e)}")
            return f"请求失败: {str(e)}"