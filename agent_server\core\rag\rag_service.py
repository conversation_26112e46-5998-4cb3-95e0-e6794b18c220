from abc import ABC, abstractmethod
from dataclasses import dataclass
import hashlib
import mimetypes
from typing import List, Optional
from agent_server.core.services.database import db_manager
from agent_server.core.services.database.crud.user_uploaded_files import user_uploaded_files_crud
from agent_server.core.services.database.schemas.user_uploaded_files import (
    FileStatus,
    UserUploadedFilesCreate,
)
from fastapi import HTTPException


def extend_mimetypes():
    mimetypes.add_type("text/markdown", ".md", True)


@dataclass
class RAGFileUploadResult:
    success: bool
    rag_file_id: str = None
    status: FileStatus = None

@dataclass
class FileUploadResult:
    success: bool
    id: Optional[str]
    rag_file_id: Optional[str] = None
    status: Optional[FileStatus] = None


@dataclass
class FileDeleteResult:
    success: bool
    deleted_files: List[str] = None
    failed_files: List[dict] = None
    total_deleted: int = 0
    total_failed: int = 0


class RAGServiceBase(ABC):
    """
    我们将 RAGFlow 的所有功能封装在一个独立的的RAGFlowService中。
    智能体项目的其他部分（如对话管理器、业务逻辑层）不直接调用 RAGFlow 的原生 API，而是通过这个统一的、语义清晰的服务层进行交互。
    RAGFlowService作为适配器，负责转换智能体项目的内部请求和 RAGFlow 的原生 API。未来如果 RAGFlow 的 API 发生变更，我们只需要修改RAGFlowService内部的实现，而无需改动智能体的上层业务代码。
    如果未来需要替换为其他 RAG 框架，我们只需重新实现一个遵循相同接口的服务即可。
    """

    def __init__(self, rag_service: str, allowed_types: List[str] = []):
        self.rag_service = rag_service
        self.allowed_types = allowed_types
        extend_mimetypes()

    async def _save_file(
        self,
        *,
        user_id: str,
        rag_file_id: str,
        file_name: str,
        file_size: int,
        file_hash: str,
        file_type: str,
        status: FileStatus,
    ):
        """
        保存文件信息到数据库

        """
        try:
            async with db_manager.session() as session:
                new_file = UserUploadedFilesCreate(
                    user_id=user_id,
                    name=file_name,
                    hash=file_hash,
                    size=file_size,
                    type=file_type,
                    rag_file_id=rag_file_id,
                    rag_service=self.rag_service,
                    status=status,
                )
                await user_uploaded_files_crud.create(session, obj_in=new_file)
                return new_file
        except Exception as e:
            print(f"Error saving file to database: {e}")
            return None

    async def upload_file(self, *, user_id: str, file_content: bytes, file_name: str):
        """
        上传文件，保存的到用户文件表
        """
        file_size = len(file_content)
        file_hash = hashlib.sha256(file_content).hexdigest()
        file_type, _ = mimetypes.guess_type(file_name)

        file_type = "unknown" if file_type is None else file_type

        # Check file type
        # 移动到配置文件
        
        if file_type not in self.allowed_types:
            raise HTTPException(
                status_code=400,
                detail=f"File type {file_type} not supported. Allowed types: {', '.join(self.allowed_types)}",
            )

        result = await self.upload_file_to_rag_service(
            file_content=file_content,
            file_name=file_name,
            file_size=file_size,
            file_hash=file_hash,
            file_type=file_type,
        )
        status = result.status
        rag_file_id = result.rag_file_id
        save_result = await self._save_file(
            user_id=user_id,
            rag_file_id=rag_file_id,
            file_name=file_name,
            file_size=file_size,
            file_hash=file_hash,
            file_type=file_type,
            status=status,
        )
        if save_result is None:
            return FileUploadResult(success=False)
        else:
            return FileUploadResult(success=True, id=save_result.id, rag_file_id=result.rag_file_id, status=status)

    async def delete_files(
        self,
        ids: List[str],
        user_id: str,
    ) -> FileDeleteResult:
        """
        删除文件，
        - 先判断文件文件是否都属于当前登录用户，
        - 再通过 ids 获取 rag_file_ids，
        - 调用 delete_file_from_rag_service 删除文件
        - 最后在从用户上传文件表删除

        Returns:
            FileDeleteResult: 包含删除结果的详细信息
        """
        if not ids:
            # 空列表视为成功
            return FileDeleteResult(
                success=True,
                deleted_files=[],
                failed_files=[],
                total_deleted=0,
                total_failed=0
            )

        try:
            # Convert user_uploaded_files IDs to RAG file IDs and verify ownership
            rag_file_ids_to_delete = []
            user_to_rag_mapping = {}  # Track mapping for detailed error handling
            valid_user_file_ids = []
            failed_files = []

            async with db_manager.session() as session:
                # Verify each user file ID and build mapping
                for user_file_id in ids:
                    file_record = await user_uploaded_files_crud.get(
                        session, id=user_file_id
                    )
                    if not file_record:
                        print(f"Error: File not found: {user_file_id}")
                        failed_files.append({
                            "file_id": user_file_id,
                            "error": "File not found"
                        })
                    elif file_record.user_id != int(user_id):
                        print(f"Error: Access denied for file: {user_file_id}")
                        failed_files.append({
                            "file_id": user_file_id,
                            "error": "Access denied"
                        })
                    else:
                        valid_user_file_ids.append(user_file_id)
                        user_to_rag_mapping[user_file_id] = file_record.rag_file_id
                        rag_file_ids_to_delete.append(file_record.rag_file_id)

            # Delete from RAG service first using the converted RAG file IDs
            if rag_file_ids_to_delete:
                try:
                    rag_success = await self.delete_file_from_rag_service(
                        user_id, rag_file_ids_to_delete
                    )
                    if not rag_success:
                        print(f"Warning: RAG service deletion returned false for files: {rag_file_ids_to_delete}, but continuing with database cleanup")
                except Exception as e:
                    print(f"Warning: RAG service deletion failed: {e}, continuing with database cleanup")

            # Delete from database using RAG file IDs, but track user file IDs for detailed logging
            deleted_files = []

            async with db_manager.session() as session:
                for user_file_id in valid_user_file_ids:
                    rag_file_id = user_to_rag_mapping[user_file_id]
                    try:
                        success = await user_uploaded_files_crud.delete_by_rag_file_id(
                            session, rag_file_id=rag_file_id
                        )
                        if success:
                            deleted_files.append(user_file_id)
                        else:
                            print(f"Warning: Database deletion failed for user_file_id: {user_file_id}, rag_file_id: {rag_file_id}")
                            failed_files.append({
                                "file_id": user_file_id,
                                "error": "Database deletion failed"
                            })
                    except Exception as e:
                        print(f"Error deleting database record for user_file_id: {user_file_id}, rag_file_id: {rag_file_id}: {e}")
                        failed_files.append({
                            "file_id": user_file_id,
                            "error": str(e)
                        })

            # 构建返回结果
            total_deleted = len(deleted_files)
            total_failed = len(failed_files)
            success = total_failed == 0

            if success:
                print(f"Successfully deleted {total_deleted} files")
            else:
                print(f"Partial deletion: {total_deleted} succeeded, {total_failed} failed")

            return FileDeleteResult(
                success=success,
                deleted_files=deleted_files,
                failed_files=failed_files,
                total_deleted=total_deleted,
                total_failed=total_failed
            )

        except Exception as e:
            print(f"Error in delete_files: {e}")
            return FileDeleteResult(
                success=False,
                deleted_files=[],
                failed_files=[{"file_id": file_id, "error": str(e)} for file_id in ids],
                total_deleted=0,
                total_failed=len(ids)
            )

    async def retrieve(
        self,
        query: str,
        ids: List[str],
        user_id: str,
    ) -> list[dict]:
        """
        召回文件内容
        - 先判断文件文件是否都属于当前登录用户
        - 通过 ids 获取 rag_file_ids
        - 调用 retrieve_from_documents 召回文档
        """
        try:
            async with db_manager.session() as session:
                # 通过 ids 获取 rag_file_ids，同时验证文件是否都属于当前登录用户
                rag_file_ids = await user_uploaded_files_crud.get_rag_file_ids_by_user_file_ids(
                    session, user_file_ids=ids, user_id=int(user_id)
                )

                # 检查是否所有请求的文件都找到了且属于当前用户
                if len(rag_file_ids) != len(ids):
                    raise ValueError("Some files not found or access denied")

                # 调用 retrieve_from_documents 召回文档
                results = await self.retrieve_from_documents(
                    query=query,
                    user_id=user_id,
                    file_ids=rag_file_ids
                )

                return results

        except Exception as e:
            print(f"Error retrieving documents: {e}")
            raise

    @abstractmethod
    async def upload_file_to_rag_service(
        self,
        *,
        file_content: bytes,
        file_name: str,
        file_size: int,
        file_hash: str,
        file_type: str,
    ) -> RAGFileUploadResult:
        """
        RAG 服务上传文件，返回文件信息
        """
        pass

    @abstractmethod
    async def parse_file(self, file_id: str):
        """
        RAG 服务解析文件
        """
        pass

    @abstractmethod
    async def get_file_status(self, file_id: str) -> dict:
        """
        查询文件状态 上传成功 等待解析 解析完成 解析失败
        """
        pass

    @abstractmethod
    async def delete_file_from_rag_service(
        self, user_id: str, file_ids: List[str]
    ) -> bool:
        """
        RAG 服务删除文件
        """
        pass

    @abstractmethod
    async def retrieve_from_documents(
        self, query: str, user_id: str, file_ids: list[str]
    ) -> list[dict]:
        """
        召回文件内容
        召回时验证是否当前用户的文件
        """
        pass
