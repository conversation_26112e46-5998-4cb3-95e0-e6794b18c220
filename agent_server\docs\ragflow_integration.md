# RagFlow RAG服务集成文档

本文档介绍如何在Agent Server中集成和使用RagFlow RAG服务。

## 概述

RagFlowRAGService是RAGServiceBase的完整实现，提供与RagFlow API的无缝集成。它支持文件上传、文档解析、状态查询、内容检索和文件删除等核心功能。

## 功能特性

- ✅ **文件上传**: 支持多种格式文档上传到RagFlow
- ✅ **文档解析**: 自动触发文档解析和向量化
- ✅ **状态查询**: 实时查询文档处理状态
- ✅ **内容检索**: 基于语义相似度的文档检索
- ✅ **文件管理**: 支持批量删除文档
- ✅ **错误处理**: 完善的错误处理和重试机制
- ✅ **异步支持**: 支持异步和同步操作
- ✅ **配置管理**: 灵活的配置管理系统
- ✅ **日志记录**: 详细的操作日志和调试信息

## 快速开始

### 1. 配置RagFlow服务

首先确保RagFlow服务正在运行，然后在配置文件中添加RagFlow配置：

```yaml
ragflow:
  base_url: "http://localhost:9380"
  api_key: "your-ragflow-api-key"
  dataset_id: "your-dataset-id"
  timeout: 120
  chunk_method: "naive"
  embedding_model: "BAAI/bge-large-zh-v1.5"
```

### 2. 初始化服务

```python
from agent_server.core.rag.ragflow import RagFlowRAGService
from agent_server.core.config.app_config import config

# 使用全局配置
service = RagFlowRAGService()

# 或使用自定义配置
from agent_server.core.config.app_config import RagFlowConfig

custom_config = RagFlowConfig(
    base_url="http://your-ragflow-server:9380",
    api_key="your-api-key",
    dataset_id="your-dataset-id"
)
service = RagFlowRAGService(ragflow_config=custom_config)
```

### 3. 基本使用

```python
# 上传文件
result = await service.upload_file(
    user_id="user123",
    file_content=file_bytes,
    file_name="document.pdf"
)

if result.success:
    file_id = result.rag_file_id

    # 触发解析 (异步方法)
    await service.parse_file(file_id)

    # 查询状态 (异步方法)
    status = await service.get_file_status(file_id)
    print(f"状态: {status['status'].value}")

    # 检索内容 (异步方法)
    results = await service.retrieve_from_documents(
        query="查询内容",
        user_id="user123",
        file_ids=[file_id]
    )

    # 删除文件 (异步方法)
    await service.delete_file_from_rag_service("user123", [file_id])
```

## API参考

### RagFlowRAGService类

#### 构造函数

```python
def __init__(self, rag_service: str = "ragflow", ragflow_config: Optional[RagFlowConfig] = None)
```

**参数:**
- `rag_service`: RAG服务标识符
- `ragflow_config`: RagFlow配置对象，为None时使用全局配置

#### 主要方法

##### upload_file_to_rag_service

```python
async def upload_file_to_rag_service(
    self, *, file_content: bytes, file_name: str, 
    file_size: int, file_hash: str, file_type: str
) -> FileUploadResult
```

上传文件到RagFlow服务。

**返回:** `FileUploadResult`对象，包含上传结果和文件ID。

##### parse_file

```python
async def parse_file(self, file_id: str) -> bool
```

触发文档解析 (异步方法)。

**返回:** 解析请求是否成功提交。

##### get_file_status

```python
async def get_file_status(self, file_id: str) -> dict
```

查询文件处理状态 (异步方法)。

**返回:** 包含状态信息的字典：
```python
{
    "status": FileStatus,      # 文件状态枚举
    "message": str,            # 状态描述
    "progress": float,         # 处理进度 (0.0-1.0)
    "error_message": str,      # 错误信息（如有）
    "chunk_count": int,        # 文档块数量
    "raw_status": str          # 原始状态值
}
```

##### delete_file_from_rag_service

```python
async def delete_file_from_rag_service(self, user_id: str, file_ids: List[str]) -> bool
```

从RagFlow删除文件 (异步方法)。

**返回:** 删除操作是否成功。

##### retrieve_from_documents

```python
async def retrieve_from_documents(
    self, query: str, user_id: str, file_ids: list[str]
) -> list[dict]
```

从指定文档检索相关内容 (异步方法)。

**返回:** 检索结果列表，每个元素包含：
```python
{
    "content": str,           # 文档块内容
    "score": float,           # 相似度分数
    "document_id": str,       # 文档ID
    "chunk_id": str,          # 文档块ID
    "metadata": dict          # 元数据信息
}
```

## 配置选项

### RagFlowConfig

```python
class RagFlowConfig(BaseModel):
    base_url: str = "http://localhost:9380"
    api_key: str
    dataset_id: str
    timeout: int = 120
    chunk_method: str = "naive"
```

**配置说明:**

- `base_url`: RagFlow服务地址
- `api_key`: RagFlow API密钥
- `dataset_id`: 默认数据集ID
- `timeout`: 请求超时时间（秒）
- `chunk_method`: 文档解析方法
- `embedding_model`: 嵌入模型名称

### 支持的文档解析方法

- `naive`: 通用解析（默认）
- `manual`: 手动解析
- `qa`: 问答格式
- `table`: 表格格式
- `paper`: 论文格式
- `book`: 书籍格式
- `laws`: 法律文档
- `presentation`: 演示文稿
- `picture`: 图片
- `one`: 单一格式
- `email`: 邮件格式

## 错误处理

服务定义了专门的异常类型：

- `RagFlowError`: 基础错误类
- `RagFlowAuthenticationError`: 认证错误
- `RagFlowNetworkError`: 网络错误
- `RagFlowAPIError`: API错误

```python
try:
    result = await service.upload_file_to_rag_service(...)
except RagFlowAuthenticationError:
    print("认证失败，请检查API密钥")
except RagFlowNetworkError:
    print("网络连接失败")
except RagFlowAPIError as e:
    print(f"API错误: {e}")
```

## 状态映射

RagFlow状态到内部FileStatus的映射：

| RagFlow状态 | 内部状态 | 说明 |
|------------|---------|------|
| "0", "uploading" | UPLOADING | 上传中 |
| "1", "ready", "completed" | READY | 已完成 |
| "2", "parsing" | PARSING | 解析中 |
| "3", "error", "failed" | ERROR | 错误 |

## 测试

### 运行单元测试

```bash
python -m unittest tests.test_ragflow_rag_service -v
```

### 运行集成测试

```bash
python examples/ragflow_integration_example.py
```

## 最佳实践

1. **配置管理**: 使用环境变量或配置文件管理敏感信息
2. **错误处理**: 始终处理可能的异常情况
3. **状态检查**: 上传后检查文档状态确保处理完成
4. **资源清理**: 及时删除不需要的文档以节省存储空间
5. **日志记录**: 启用详细日志以便调试和监控

## 故障排除

### 常见问题

1. **认证失败**: 检查API密钥是否正确
2. **数据集不存在**: 确认dataset_id是否有效
3. **网络超时**: 调整timeout配置或检查网络连接
4. **文档解析失败**: 检查文档格式是否支持

### 调试技巧

1. 启用详细日志：设置环境变量 `LOG_LEVEL=DEBUG`
2. 检查RagFlow服务状态和日志
3. 使用集成测试脚本验证配置
4. 查看API响应的详细错误信息

## 更新日志

- v1.0.0: 初始版本，实现所有基础功能
- 支持文件上传、解析、状态查询、检索和删除
- 完善的错误处理和日志记录
- 全面的单元测试覆盖
