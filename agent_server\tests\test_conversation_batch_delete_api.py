import unittest
from unittest.mock import AsyncMock, patch
from fastapi.testclient import TestClient

from agent_server.core.api.conversation import router
from agent_server.core.vo.user_vo import UserVO
from agent_server.core.services.database.schemas.conversation import ConversationBatchDelete


class TestConversationBatchDeleteAPI(unittest.TestCase):
    """测试会话批量删除API接口"""

    def setUp(self):
        """设置测试环境"""
        from fastapi import FastAPI
        self.app = FastAPI()
        self.app.include_router(router)
        self.client = TestClient(self.app)
        
        # 模拟用户
        self.mock_user = UserVO(userId=12345, username="test_user")

    @patch('core.api.conversation.check_user')
    @patch('core.api.conversation.conversation_curd.batch_soft_delete')
    @patch('core.api.conversation.db_manager.session')
    def test_batch_delete_success(self, mock_session, mock_batch_delete, mock_check_user):
        """测试批量删除成功"""
        # 设置模拟
        mock_check_user.return_value = self.mock_user
        mock_session.return_value.__aenter__.return_value = AsyncMock()
        mock_batch_delete.return_value = {
            "deleted_count": 3,
            "deleted_ids": ["id1", "id2", "id3"],
            "not_found_ids": [],
            "total_requested": 3
        }

        # 发送请求
        response = self.client.post(
            "/conversation/batch-delete",
            json={"conversation_ids": ["id1", "id2", "id3"]}
        )

        # 验证响应
        self.assertEqual(response.status_code, 200)
        data = response.json()
        self.assertEqual(data["message"], "成功删除 3 个会话")
        self.assertEqual(data["data"]["deleted_count"], 3)
        self.assertEqual(len(data["data"]["deleted_ids"]), 3)

    @patch('core.api.conversation.check_user')
    @patch('core.api.conversation.conversation_curd.batch_soft_delete')
    @patch('core.api.conversation.db_manager.session')
    def test_batch_delete_partial_success(self, mock_session, mock_batch_delete, mock_check_user):
        """测试批量删除部分成功"""
        # 设置模拟
        mock_check_user.return_value = self.mock_user
        mock_session.return_value.__aenter__.return_value = AsyncMock()
        mock_batch_delete.return_value = {
            "deleted_count": 2,
            "deleted_ids": ["id1", "id2"],
            "not_found_ids": ["id3", "id4"],
            "total_requested": 4
        }

        # 发送请求
        response = self.client.post(
            "/conversation/batch-delete",
            json={"conversation_ids": ["id1", "id2", "id3", "id4"]}
        )

        # 验证响应
        self.assertEqual(response.status_code, 200)
        data = response.json()
        self.assertIn("成功删除 2 个会话", data["message"])
        self.assertIn("2 个会话未找到或已删除", data["message"])
        self.assertEqual(data["data"]["deleted_count"], 2)
        self.assertEqual(len(data["data"]["not_found_ids"]), 2)

    @patch('core.api.conversation.check_user')
    def test_batch_delete_empty_list(self, mock_check_user):
        """测试批量删除空列表"""
        # 设置模拟
        mock_check_user.return_value = self.mock_user

        # 发送请求
        response = self.client.post(
            "/conversation/batch-delete",
            json={"conversation_ids": []}
        )

        # 验证响应
        self.assertEqual(response.status_code, 400)
        data = response.json()
        self.assertEqual(data["detail"], "会话ID列表不能为空")

    @patch('core.api.conversation.check_user')
    def test_batch_delete_too_many_ids(self, mock_check_user):
        """测试批量删除ID过多"""
        # 设置模拟
        mock_check_user.return_value = self.mock_user

        # 创建超过100个ID的列表
        large_id_list = [f"id{i}" for i in range(101)]

        # 发送请求
        response = self.client.post(
            "/conversation/batch-delete",
            json={"conversation_ids": large_id_list}
        )

        # 验证响应
        self.assertEqual(response.status_code, 400)
        data = response.json()
        self.assertEqual(data["detail"], "单次批量删除不能超过100个会话")

    @patch('core.api.conversation.check_user')
    @patch('core.api.conversation.conversation_curd.batch_soft_delete')
    @patch('core.api.conversation.db_manager.session')
    def test_batch_delete_no_conversations_found(self, mock_session, mock_batch_delete, mock_check_user):
        """测试批量删除时没有找到任何会话"""
        # 设置模拟
        mock_check_user.return_value = self.mock_user
        mock_session.return_value.__aenter__.return_value = AsyncMock()
        mock_batch_delete.return_value = {
            "deleted_count": 0,
            "deleted_ids": [],
            "not_found_ids": ["id1", "id2", "id3"],
            "total_requested": 3
        }

        # 发送请求
        response = self.client.post(
            "/conversation/batch-delete",
            json={"conversation_ids": ["id1", "id2", "id3"]}
        )

        # 验证响应
        self.assertEqual(response.status_code, 200)
        data = response.json()
        self.assertEqual(data["code"], "404")
        self.assertEqual(data["message"], "没有找到可删除的会话")
        self.assertEqual(data["data"]["deleted_count"], 0)

    def test_batch_delete_invalid_request_format(self):
        """测试批量删除无效请求格式"""
        # 发送无效格式的请求
        response = self.client.post(
            "/conversation/batch-delete",
            json={"invalid_field": ["id1", "id2"]}
        )

        # 验证响应
        self.assertEqual(response.status_code, 422)  # Validation error

    def test_conversation_batch_delete_schema(self):
        """测试ConversationBatchDelete schema"""
        # 测试有效数据
        valid_data = {"conversation_ids": ["id1", "id2", "id3"]}
        batch_delete = ConversationBatchDelete(**valid_data)
        self.assertEqual(batch_delete.conversation_ids, ["id1", "id2", "id3"])

        # 测试空列表
        empty_data = {"conversation_ids": []}
        batch_delete_empty = ConversationBatchDelete(**empty_data)
        self.assertEqual(batch_delete_empty.conversation_ids, [])


if __name__ == "__main__":
    unittest.main()
