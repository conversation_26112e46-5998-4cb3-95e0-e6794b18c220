def create_message_chunks(message: str, *, package_type: int = 0, step: int = 10):
    """
    创建 mermaid 消息块
    将消息内容拆分为多个块，每个块的字符数量为 step 个字符
    """
    message_chunks = [
        {
            "data": "",
            "is_last": False,
            "package_type": package_type,
            "is_new_package": True,
        }
    ]
    for i in range(0, len(message), step):
        message_chunks.append(
            {
                "data": message[i : i + step],
                "is_last": False,
                "package_type": package_type,
                "is_new_package": False,
            }
        )
    message_chunks[-1]["is_last"] = True
    return message_chunks
