"""change field name rag_server to rag_service

Revision ID: f8329b9ad1f3
Revises: ead30a9c42cf
Create Date: 2025-09-17 10:50:05.842360

"""
from typing import Sequence, Union

from alembic import op
import sqlalchemy as sa

import sqlmodel

# revision identifiers, used by Alembic.
revision: str = 'f8329b9ad1f3'
down_revision: Union[str, None] = 'ead30a9c42cf'
branch_labels: Union[str, Sequence[str], None] = None
depends_on: Union[str, Sequence[str], None] = None


def upgrade() -> None:
    """Upgrade schema."""
    # ### commands auto generated by Alembic - please adjust! ###
    op.add_column('user_uploaded_files', sa.Column('rag_service', sqlmodel.sql.sqltypes.AutoString(length=100), nullable=False))
    op.drop_column('user_uploaded_files', 'rag_server')
    # ### end Alembic commands ###


def downgrade() -> None:
    """Downgrade schema."""
    # ### commands auto generated by Alembic - please adjust! ###
    op.add_column('user_uploaded_files', sa.Column('rag_server', sa.VARCHAR(length=100), server_default=sa.text("'ragflow'::character varying"), autoincrement=False, nullable=False))
    op.drop_column('user_uploaded_files', 'rag_service')
    # ### end Alembic commands ###
