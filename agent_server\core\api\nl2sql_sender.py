from typing import  Dict, Any, Optional
from agent_server.core.base.http_sender import HttpBaseSender
from agent_server.core.config.app_logger import logger

################################
# 动态页面问答小助手智能体
################################
class NL2SqlSender(HttpBaseSender):
    def __init__(self, agent_code: str):
        super().__init__(agent_code)

    def _process_result(self, result: Dict[str, Any]) -> Optional[str]:
        """自定义结果处理器"""
        if result.get("success"):
            raw_sql = result['data'].get('sql', '')
            
            if raw_sql:
                formatted_sql = self._format_sql(raw_sql)
                content = f"✅ 生成SQL：\n```sql\n{formatted_sql}\n```"
            
            # 添加解释说明
            if explanation := result['data'].get('analysis'):
                content += f"\n\n📝 分析：{explanation}"
                
        else:
            error_msg = result.get('error', '未知错误')
            content = f"❌ 错误：{error_msg}"

        return content

    def _format_sql(self, sql: str) -> str:
        """SQL格式化工具"""
        # 简单缩进处理
        return sql.replace('\\n', '\n').replace('\\t', '    ').replace("\\'", "'")
    
    def _get_payload(self, question: str, extend_params: dict) -> dict:
        """自定义payload逻辑"""
        return {
            "query": question,
            "connection_id": extend_params.get('dataSourceId')
        }

    
    
