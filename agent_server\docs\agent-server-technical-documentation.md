# Agent Server 技术文档

## 📋 目录

1. [项目概述](#项目概述)
2. [系统架构](#系统架构)
3. [核心模块](#核心模块)
4. [功能特性](#功能特性)
5. [技术栈](#技术栈)
6. [部署指南](#部署指南)
7. [API 文档](#api-文档)
8. [开发指南](#开发指南)

## 🎯 项目概述

Agent Server 是一个全面的 AI 智能体框架，提供企业级的 AI 应用开发和部署解决方案。

### 核心特性

- 🤖 **多智能体支持**：支持 LangChain、LangGraph 等多种智能体框架
- 🔌 **MCP 协议集成**：Model Context Protocol 支持，实现工具和资源的标准化访问
- 🗄️ **企业级数据库**：PostgreSQL + Redis 双重存储架构
- 🌐 **RESTful API**：完整的 FastAPI 接口体系
- 🔐 **安全认证**：灵活的认证适配器系统
- 📊 **RAGs 知识库**：检索增强生成系统，支持向量检索和全文检索
- 🚀 **高性能**：异步架构，支持高并发访问
- 🔧 **可扩展**：插件化架构，支持自定义模块

### 版本信息

- **版本**：1.0.0
- **作者**：Elephant
- **邮箱**：<EMAIL>
- **许可证**：MIT

## 🏗️ 系统架构

```mermaid
graph TB
    subgraph "客户端层"
        A[Web 前端]
        B[移动端]
        C[第三方应用]
    end
    
    subgraph "API 网关层"
        D[Gateway Router]
        E[认证中间件]
        F[负载均衡]
    end
    
    subgraph "应用服务层"
        G[Agent API]
        H[Chat API]
        I[RAGs API]
        J[管理 API]
    end
    
    subgraph "核心业务层"
        K[Agent Factory]
        L[LLM Client]
        M[MCP Factory]
        N[RAGs Service]
    end
    
    subgraph "数据存储层"
        O[PostgreSQL]
        P[Redis]
        Q[Milvus]
        R[文件存储]
    end
    
    subgraph "外部服务"
        S[LLM 服务]
        T[嵌入模型]
        U[重排序服务]
    end
    
    A --> D
    B --> D
    C --> D
    D --> E
    E --> F
    F --> G
    F --> H
    F --> I
    F --> J
    G --> K
    H --> L
    I --> N
    K --> M
    L --> S
    N --> T
    N --> U
    K --> O
    L --> P
    N --> Q
    J --> R
```

## 🧩 核心模块

### 1. 核心框架 (core/)

#### 1.1 API 层 (core/api/)

**主要组件：**
- `router.py` - 路由注册和管理
- `router_registry.py` - 动态路由注册系统
- `agent.py` - 智能体管理接口
- `chat.py` - 聊天对话接口
- `conversation.py` - 会话管理接口
- `message.py` - 消息处理接口
- `prompt_template.py` - 提示词模板管理
- `login.py` - 用户认证接口

**核心功能：**
- RESTful API 接口定义
- 请求参数验证
- 响应数据格式化
- 错误处理和异常管理

#### 1.2 认证系统 (core/auth/)

**组件架构：**
```python
# 认证适配器接口
class AuthAdapter:
    def parse_user_data(self, raw_data: dict) -> UserVO

# 默认认证适配器
class DefaultAuthAdapter(AuthAdapter):
    # 实现用户数据解析逻辑

# 认证工厂
class AuthAdapterFactory:
    # 动态加载认证适配器
```

**特性：**
- 🔌 **插件化设计**：支持自定义认证适配器
- 🛡️ **安全机制**：Token 验证和权限控制
- 🔄 **缓存优化**：Redis 缓存用户信息
- 📊 **审计日志**：完整的认证日志记录

#### 1.3 配置系统 (core/config/)

**配置文件结构：**
```yaml
# 模型配置
ai:
  models:
    default: volcengine
    qwen: {...}
    ollama: {...}

# 认证配置
auth:
  adapter: agent_server.core.auth.default_auth_adapter.DefaultAuthAdapter
  auth_url: http://example.com/api/user/current

# 数据库配置
database:
  url: postgresql+asyncpg://user:pass@host:port/db

# Redis 配置
redis:
  host: localhost
  port: 6379

# Agent 配置
agents:
  - name: dynamic-page-creator
    impl: agent_server.core.api.dynamic_creator_sender.PageCreatorSender
    exclude_tools: [...]

# MCP 服务器配置
mcp_servers:
  - key: dynamic-page-server
    type: stdio
    url: mcpserver/mcp_server.py
    enabled: true
```

#### 1.4 数据库服务 (core/services/database/)

**数据模型：**
- `ConversationTable` - 会话记录
- `MessageTable` - 消息内容
- `AIAgentTable` - 智能体配置
- `PromptTemplateTable` - 提示词模板
- `PopularQuestionTable` - 热门问题
- `ModelManagementTable` - 模型管理

**CRUD 操作：**
```python
# 基础 CRUD 类
class CRUDBase[ModelType, CreateSchemaType, UpdateSchemaType]:
    async def create(self, db: AsyncSession, obj_input: CreateSchemaType)
    async def get(self, db: AsyncSession, _id: Any)
    async def update(self, db: AsyncSession, db_obj: ModelType, obj_input: UpdateSchemaType)
    async def delete(self, db: AsyncSession, _id: Any)
```

#### 1.5 工厂模式 (core/factory/)

**Agent 工厂：**
```python
class AgentFactory:
    """智能体工厂中枢"""
    
    @classmethod
    async def load_agents(cls):
        """从数据库加载智能体配置"""
    
    @classmethod
    def get_sender(cls, agent_code: str):
        """获取智能体发送器实例"""
    
    @classmethod
    def register_sender_class(cls, agent_code: str, sender_class):
        """注册自定义发送器类"""
```

**MCP 工厂：**
```python
class McpFactory:
    """MCP 客户端工厂"""
    
    async def create_mcp_clients(self) -> List[McpClient]:
        """创建 MCP 客户端实例"""
    
    async def close_clients(self):
        """关闭所有客户端连接"""
```

### 2. LLM 客户端 (llmclient/)

**核心功能：**
- 🤖 **多模型支持**：OpenAI、Azure OpenAI、自定义模型
- 🔧 **工具集成**：自动发现和注册 MCP 工具
- 💬 **对话管理**：会话历史和上下文管理
- 🔄 **流式响应**：支持实时流式输出

**使用示例：**
```python
# 初始化 LLM 客户端
client = LLMClient(
    request=request,
    model_name="gpt-4",
    user_id="user123",
    agent_id="customer-service"
)

# 异步初始化
await client.init_async()

# 获取工具列表
tools = client.get_tools()

# 获取模型信息
model_info = client.get_model_info()
```

### 3. RAGs 知识库系统 (rags/)

#### 3.1 核心服务

**嵌入服务 (EmbeddingService)：**
- 文本向量化处理
- 批量嵌入计算
- 多种嵌入模型支持

**向量存储 (VectorStore)：**
- Milvus 向量数据库集成
- 向量索引和检索
- 相似度计算

**检索服务 (RetrievalService)：**
- 向量检索
- 全文检索
- 混合检索策略

**重排序服务 (RerankService)：**
- 检索结果重排序
- 相关性优化
- 多种重排序算法

#### 3.2 数据模型

```python
# 知识库模型
class KnowledgeBase(BaseModel):
    id: str
    name: str
    description: str
    collection_name: str
    embedding_model: str
    dimension: int

# 文档模型
class Document(BaseModel):
    id: str
    knowledge_base_id: str
    title: str
    content: str
    metadata: Dict[str, Any]
    status: DocumentStatus

# 文档分段模型
class DocumentChunk(BaseModel):
    id: str
    document_id: str
    content: str
    embedding: List[float]
    metadata: Dict[str, Any]
```

#### 3.3 API 接口

**知识库管理：**
- `POST /rags/knowledge-bases` - 创建知识库
- `GET /rags/knowledge-bases` - 获取知识库列表
- `GET /rags/knowledge-bases/{kb_id}` - 获取知识库详情
- `DELETE /rags/knowledge-bases/{kb_id}` - 删除知识库

**文档管理：**
- `POST /rags/knowledge-bases/{kb_id}/documents` - 上传文档
- `GET /rags/knowledge-bases/{kb_id}/documents` - 获取文档列表
- `DELETE /rags/documents/{doc_id}` - 删除文档

**检索接口：**
- `POST /rags/knowledge-bases/{kb_id}/search` - 知识库检索

### 4. MCP 协议支持

#### 4.1 MCP 客户端 (mcpclient/)

**功能特性：**
- 🔌 **协议支持**：完整的 MCP 协议实现
- 🛠️ **工具调用**：远程工具执行
- 📡 **通信方式**：stdio、SSE 等多种传输方式
- 🔄 **连接管理**：自动重连和错误恢复

#### 4.2 MCP 服务器 (mcpserver/)

**内置工具：**
- `say_hello` - 问候语生成
- `get_weather` - 天气查询
- `call_nl2sql_function` - 自然语言转 SQL
- `query_company_info` - 企业信息查询
- `save_page_config` - 页面配置保存

### 5. 网关系统 (gateway/)

**核心功能：**
- 🚪 **请求路由**：智能请求分发
- 🔐 **统一认证**：集中式认证管理
- ⚖️ **负载均衡**：多实例负载分发
- 📊 **监控统计**：请求统计和性能监控

## 🚀 功能特性

### 1. 智能体管理

- **多框架支持**：LangChain、LangGraph、Dify、HTTP
- **动态配置**：运行时配置更新
- **工具集成**：自动工具发现和注册
- **会话管理**：多用户会话隔离

### 2. 对话系统

- **实时对话**：WebSocket 实时通信
- **历史记录**：完整的对话历史
- **上下文管理**：智能上下文维护
- **多轮对话**：支持复杂多轮交互

### 3. 知识库系统

- **文档处理**：多格式文档支持
- **向量检索**：高效语义检索
- **全文检索**：传统关键词检索
- **混合检索**：多策略融合检索

### 4. 用户管理

- **认证授权**：灵活的权限控制
- **用户隔离**：多租户数据隔离
- **审计日志**：完整的操作记录
- **会话管理**：安全的会话控制

### 5. 系统管理

- **配置管理**：动态配置更新
- **日志系统**：结构化日志记录
- **监控告警**：系统状态监控
- **性能优化**：缓存和优化策略

## 🛠️ 技术栈

### 后端技术

- **Web 框架**：FastAPI 0.104+
- **异步支持**：asyncio、aiohttp
- **数据库**：PostgreSQL 14+、Redis 6+
- **向量数据库**：Milvus 2.3+
- **ORM**：SQLModel、SQLAlchemy 2.0
- **认证**：JWT、OAuth2
- **日志**：Loguru
- **配置**：Pydantic、YAML

### AI 技术

- **LLM 框架**：LangChain 0.1+、LangGraph
- **模型支持**：OpenAI、Azure OpenAI、Qwen、DeepSeek
- **向量化**：OpenAI Embeddings、本地嵌入模型
- **协议**：MCP (Model Context Protocol)

### 开发工具

- **包管理**：uv、pip
- **代码质量**：pylint、black、isort
- **测试**：pytest、pytest-asyncio
- **文档**：Markdown、Mermaid
- **容器化**：Docker、Docker Compose

## 📦 部署指南

### 环境要求

- **Python**：3.11+
- **PostgreSQL**：14+
- **Redis**：6+
- **Milvus**：2.3+ (可选，用于 RAGs)

### 安装步骤

1. **克隆项目**
```bash
git clone <repository-url>
cd agent-server
```

2. **安装依赖**
```bash
# 使用 uv (推荐)
uv sync

# 或使用 pip
pip install -r requirements.txt
```

3. **配置环境**
```bash
# 复制配置文件
cp config/application.yml.example config/application.yml

# 编辑配置文件
vim config/application.yml
```

4. **初始化数据库**
```bash
# 创建数据库表
python -c "
from agent_server.core.services.database import db_manager
import asyncio
asyncio.run(db_manager.init_db())
"
```

5. **启动服务**
```bash
# 开发环境
uvicorn main:app --reload --port 8000

# 生产环境
uvicorn main:app --host 0.0.0.0 --port 8000 --workers 4
```

### Docker 部署

```dockerfile
# Dockerfile
FROM python:3.11-slim

WORKDIR /app
COPY . .

RUN pip install -r requirements.txt

EXPOSE 8000
CMD ["uvicorn", "main:app", "--host", "0.0.0.0", "--port", "8000"]
```

```yaml
# docker-compose.yml
version: '3.8'
services:
  agent-server:
    build: .
    ports:
      - "8000:8000"
    environment:
      - DATABASE_URL=************************************/agent_db
      - REDIS_URL=redis://redis:6379
    depends_on:
      - postgres
      - redis
  
  postgres:
    image: postgres:14
    environment:
      POSTGRES_DB: agent_db
      POSTGRES_USER: user
      POSTGRES_PASSWORD: pass
    volumes:
      - postgres_data:/var/lib/postgresql/data
  
  redis:
    image: redis:6-alpine
    volumes:
      - redis_data:/data

volumes:
  postgres_data:
  redis_data:
```

## 📚 API 文档

### 认证接口

**登录**
```http
POST /api/public/login
Content-Type: application/json

{
  "username": "admin",
  "password": "password"
}
```

### 智能体接口

**获取智能体列表**
```http
GET /api/agent
Authorization: Bearer <token>
```

**刷新智能体配置**
```http
POST /api/public/agent/refresh/{agent_code}
```

### 对话接口

**发起对话**
```http
POST /api/chat/completion
Authorization: Bearer <token>
Content-Type: application/json

{
  "message": "你好",
  "agent_code": "customer-service",
  "conversation_id": "conv-123"
}
```

### 知识库接口

**创建知识库**
```http
POST /api/rags/knowledge-bases
Authorization: Bearer <token>
Content-Type: application/json

{
  "name": "技术文档库",
  "description": "存储技术文档的知识库",
  "embedding_model": "text-embedding-ada-002"
}
```

**知识库检索**
```http
POST /api/rags/knowledge-bases/{kb_id}/search
Authorization: Bearer <token>
Content-Type: application/json

{
  "query": "如何部署系统",
  "search_type": "hybrid",
  "top_k": 10
}
```

## 🔧 开发指南

### 项目结构

```
agent-server/
├── core/                   # 核心框架
│   ├── api/               # API 接口层
│   ├── auth/              # 认证系统
│   ├── config/            # 配置管理
│   ├── services/          # 业务服务
│   └── factory/           # 工厂模式
├── rags/                  # RAGs 知识库系统
│   ├── api/               # RAGs API
│   ├── services/          # RAGs 服务
│   ├── models/            # 数据模型
│   └── utils/             # 工具函数
├── llmclient/             # LLM 客户端
├── mcpclient/             # MCP 客户端
├── mcpserver/             # MCP 服务器
├── gateway/               # API 网关
├── config/                # 配置文件
├── docs/                  # 文档
└── tests/                 # 测试用例
```

### 添加新的智能体

1. **创建发送器类**
```python
# core/api/my_custom_sender.py
from agent_server.core.base.base_sender import BaseSender

class MyCustomSender(BaseSender):
    async def _generate_content(self, message: str, **kwargs):
        # 实现自定义逻辑
        yield {"type": "text", "content": "响应内容"}
```

2. **注册智能体**
```python
# 在数据库中添加智能体配置
# 或在配置文件中添加
agents:
  - name: my-custom-agent
    impl: agent_server.core.api.my_custom_sender.MyCustomSender
```

### 添加新的 MCP 工具

```python
# mcpserver/mcp_server.py
@mcp.tool()
async def my_custom_tool(param1: str, param2: int = 10) -> str:
    """自定义工具描述"""
    # 实现工具逻辑
    return f"处理结果: {param1} - {param2}"
```

### 扩展认证系统

```python
# core/auth/custom_auth_adapter.py
from agent_server.core.auth.adapter import AuthAdapter
from agent_server.core.vo.user_vo import UserVO

class CustomAuthAdapter(AuthAdapter):
    def parse_user_data(self, raw_data: dict) -> UserVO:
        # 实现自定义用户数据解析
        return UserVO(
            userId=raw_data["id"],
            username=raw_data["name"],
            permissions=set(raw_data["roles"])
        )
```

### 路由注册

使用新的动态路由注册系统：

```python
# plugins/my_plugin.py
from fastapi import APIRouter
from agent_server.core.api.router_registry import router_register

# 注册路由模块
router_register(
    prefix="/my-plugin",
    tags=["我的插件"],
    description="自定义插件API"
)

router = APIRouter()

@router.get("/hello")
async def hello():
    return {"message": "Hello from my plugin!"}
```

## 📊 性能优化

### 数据库优化

- **连接池**：配置合适的连接池大小
- **索引优化**：为查询字段添加索引
- **查询优化**：使用异步查询和批量操作
- **缓存策略**：Redis 缓存热点数据

### 内存优化

- **对象池**：重用对象实例
- **垃圾回收**：及时释放不用的资源
- **流式处理**：大数据流式处理
- **异步 I/O**：避免阻塞操作

### 并发优化

- **异步架构**：全异步处理
- **连接复用**：HTTP 连接复用
- **任务队列**：后台任务处理
- **负载均衡**：多实例部署

## 🔍 监控和日志

### 日志系统

- **结构化日志**：JSON 格式日志
- **日志级别**：DEBUG、INFO、WARNING、ERROR
- **日志轮转**：按时间和大小轮转
- **日志聚合**：集中式日志收集

### 监控指标

- **系统指标**：CPU、内存、磁盘使用率
- **应用指标**：请求量、响应时间、错误率
- **业务指标**：用户活跃度、对话成功率
- **数据库指标**：连接数、查询性能

## 🛡️ 安全考虑

### 认证安全

- **Token 管理**：JWT Token 过期和刷新
- **权限控制**：基于角色的访问控制
- **会话安全**：安全的会话管理
- **API 限流**：防止 API 滥用

### 数据安全

- **数据加密**：敏感数据加密存储
- **传输安全**：HTTPS 传输加密
- **数据备份**：定期数据备份
- **访问审计**：完整的访问日志

## 🤝 贡献指南

### 开发流程

1. Fork 项目
2. 创建功能分支
3. 编写代码和测试
4. 提交 Pull Request
5. 代码审查和合并

### 代码规范

- **PEP 8**：遵循 Python 代码规范
- **类型注解**：使用类型提示
- **文档字符串**：完整的函数文档
- **单元测试**：编写测试用例

### 提交规范

```
feat: 添加新功能
fix: 修复 bug
docs: 更新文档
style: 代码格式调整
refactor: 代码重构
test: 添加测试
chore: 构建过程或辅助工具的变动
```

## 📞 支持和联系

- **GitHub Issues**：[项目 Issues](https://github.com/your-repo/issues)
- **邮箱支持**：<EMAIL>
- **文档站点**：[在线文档](https://your-docs-site.com)

---

## 📖 附录

### A. 配置文件详解

#### A.1 完整配置示例

```yaml
# config/application.yml
# 模型配置
ai:
  models:
    default: volcengine  # 默认模型标识符
    qwen:
      name: qwen-max
      api_key: your-qwen-api-key
      base_url: https://dashscope.aliyuncs.com/api/v1/services/aigc/text-generation/generation
    ollama:
      name: llama3
      api_key: dummy-key
      base_url: http://localhost:11434/v1
    volcengine:
      name: volcengine/deepseek-v3
      api_key: your-volcengine-api-key
      base_url: http://your-proxy:3000/v1

# 认证配置
auth:
  adapter: agent_server.core.auth.default_auth_adapter.DefaultAuthAdapter
  auth_url: http://your-auth-service:8002/api/user/basic/current

# 数据库配置
database:
  url: postgresql+asyncpg://postgres:postgres@localhost:5432/agent_db

# Redis 配置
redis:
  host: localhost
  port: 6379
  password: your-redis-password
  db: 0

# Agent 配置
agents:
  - name: dynamic-page-creator
    impl: agent_server.core.api.dynamic_creator_sender.PageCreatorSender
    exclude_tools:
      - call_nl2sql_function
      - query_company_info
  - name: nl2sql-agent
    impl: agent_server.core.api.nl2sql_sender.NL2SqlSender
  - name: elephant-agent
    impl: agent_server.core.api.elephant_sender.ElephantSender
    checkpoint: memory

# MCP 服务器配置
mcp_servers:
  - key: dynamic-page-server
    type: stdio
    url: mcpserver/mcp_server.py
    enabled: true
  - key: other-server
    type: sse
    url: http://external-service:5602/sse/1
    enabled: false

# 网关配置
gateway:
  services:
    - name: cluster
      url: http://business-system:8002/api
    - name: nl2sql
      url: http://nl2sql-service:8000/api

# 路由注册配置
routers:
  auto_discovery:
    enabled: true
    packages:
      - "rags"
      - "plugins"
  manual:
    - module_path: "rags.api.knowledge_base_api"
      router_name: "router"
      prefix: "/rags"
      tags: ["RAGs知识库"]
      enabled: true
      description: "RAGs知识库管理API"
      priority: 100

# RAGs 知识库系统配置
rags:
  enabled: true
  vector_store:
    type: "milvus"
    milvus:
      uri: "http://localhost:19530"
      user: ""
      password: ""
      db_name: "rags_db"
      collection_prefix: "kb_"
  embedding:
    base_url: "http://your-proxy:3000/v1"
    api_key: "your-embedding-api-key"
    model: "volcengine/doubao-embedding"
    max_tokens: 8192
    batch_size: 100
  rerank:
    base_url: "http://your-proxy:3000/v1"
    api_key: "your-rerank-api-key"
    model: "aliyun/gte-rerank-v2"
    max_tokens: 4096
  text_splitter:
    chunk_size: 1024
    chunk_overlap: 51
    separators: ["\n\n"]
  retrieval:
    vector_search:
      top_k: 10
      score_threshold: 0.7
    fulltext_search:
      top_k: 10
      score_threshold: 0.5
    hybrid_search:
      top_k: 20
      final_top_k: 10
      vector_weight: 0.7
      fulltext_weight: 0.3
      score_threshold: 0.6

# 后台管理系统配置
admin:
  users:
    admin:
      password: "$2b$12$w76kENKnxdT5nCEZe7Yneezn358is/FRcdfD3yhGZOBvo4GCq358u"  # admin123
      role: "super_admin"
    manager:
      password: "$2b$12$qDcOjbYiMZ.FO6bBJVeNA.Web/dbTMVN3axfgZVFq4DB4zOu0Q2Q2"  # manager123
      role: "admin"
  system:
    title: "Agent Server 管理后台"
    session_timeout: 3600
    port: 8001
    storage_secret: "your-secret-key"

# 提示词模板目录
prompt_dir: ./prompts

# 业务系统配置
business_system:
  host: your-business-host
  port: 8002

# 通用图片预览地址配置
commonPictureUrl: http://your-business-host:8002/api/common/getPictureUrls
```

### B. 数据库表结构

#### B.1 核心表结构

**会话表 (conversation)**
```sql
CREATE TABLE conversation (
    id VARCHAR PRIMARY KEY,
    title VARCHAR DEFAULT 'Untitled',
    user_id INTEGER NOT NULL,
    current_agent_code VARCHAR NOT NULL,
    task_id VARCHAR,
    is_deleted BOOLEAN DEFAULT FALSE,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

CREATE INDEX idx_conversation_user_id ON conversation(user_id);
CREATE INDEX idx_conversation_created_at ON conversation(created_at);
```

**消息表 (message)**
```sql
CREATE TABLE message (
    id VARCHAR PRIMARY KEY,
    agent_code VARCHAR,
    conversation_id VARCHAR NOT NULL,
    message_type VARCHAR DEFAULT 'ai',
    content JSONB NOT NULL,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    like_count INTEGER DEFAULT 0,
    dislike_count INTEGER DEFAULT 0,
    user_rating VARCHAR,
    data_object JSONB,
    content_tsvector TSVECTOR
);

CREATE INDEX idx_message_conversation_id ON message(conversation_id);
CREATE INDEX idx_message_agent_code ON message(agent_code);
CREATE INDEX idx_message_created_at ON message(created_at);
CREATE INDEX idx_message_content_tsvector ON message USING gin(content_tsvector);
```

**智能体表 (ai_agent)**
```sql
CREATE TABLE ai_agent (
    id SERIAL PRIMARY KEY,
    agent_code VARCHAR UNIQUE NOT NULL,
    agent_name VARCHAR NOT NULL,
    target_type VARCHAR NOT NULL,
    base_url VARCHAR,
    api_key VARCHAR,
    router_url VARCHAR,
    prompt_text TEXT,
    agent_desc TEXT,
    disabled VARCHAR DEFAULT '0',
    classify VARCHAR,
    recursion_limit INTEGER,
    icon VARCHAR,
    create_time TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    update_time TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

CREATE INDEX idx_ai_agent_code ON ai_agent(agent_code);
CREATE INDEX idx_ai_agent_disabled ON ai_agent(disabled);
```

#### B.2 RAGs 表结构

**知识库表 (ai_knowledge_base)**
```sql
CREATE TABLE ai_knowledge_base (
    id VARCHAR PRIMARY KEY,
    name VARCHAR NOT NULL,
    description TEXT,
    collection_name VARCHAR UNIQUE NOT NULL,
    embedding_model VARCHAR NOT NULL,
    dimension INTEGER NOT NULL,
    chunk_size INTEGER DEFAULT 1024,
    chunk_overlap INTEGER DEFAULT 200,
    separators TEXT DEFAULT '["\\n\\n", "\\n"]',
    create_by VARCHAR NOT NULL,
    update_by VARCHAR NOT NULL,
    create_time TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    update_time TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);
```

**文档表 (ai_document)**
```sql
CREATE TABLE ai_document (
    id VARCHAR PRIMARY KEY,
    knowledge_base_id VARCHAR NOT NULL,
    title VARCHAR NOT NULL,
    content TEXT NOT NULL,
    file_path VARCHAR,
    file_size BIGINT,
    file_type VARCHAR,
    metadata JSONB,
    status VARCHAR DEFAULT 'processing',
    create_by VARCHAR NOT NULL,
    update_by VARCHAR NOT NULL,
    create_time TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    update_time TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    FOREIGN KEY (knowledge_base_id) REFERENCES ai_knowledge_base(id)
);
```

### C. API 接口详细说明

#### C.1 认证接口

**用户登录**
```http
POST /api/public/login
Content-Type: application/json

Request:
{
  "username": "admin",
  "password": "admin123"
}

Response:
{
  "code": 200,
  "message": "登录成功",
  "data": {
    "token": "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9...",
    "user": {
      "userId": "1",
      "username": "admin",
      "nickName": "管理员",
      "permissions": ["admin", "user"]
    }
  }
}
```

#### C.2 智能体接口

**获取智能体列表**
```http
GET /api/agent
Authorization: Bearer <token>

Response:
{
  "code": 200,
  "message": "查询成功",
  "data": [
    {
      "id": 1,
      "agent_code": "customer-service",
      "agent_name": "客服助手",
      "target_type": "LANGCHAIN",
      "agent_desc": "智能客服助手",
      "icon": "http://example.com/icon.png"
    }
  ]
}
```

**刷新智能体配置**
```http
POST /api/public/agent/refresh/{agent_code}

Response:
{
  "code": 200,
  "message": "刷新成功",
  "data": {
    "agent_code": "customer-service",
    "status": "refreshed"
  }
}
```

#### C.3 对话接口

**发起对话**
```http
POST /api/chat/completion
Authorization: Bearer <token>
Content-Type: application/json

Request:
{
  "message": "你好，我需要帮助",
  "agent_code": "customer-service",
  "conversation_id": "conv-123",
  "stream": true
}

Response (Stream):
data: {"type": "text", "content": "您好！"}
data: {"type": "text", "content": "我是智能客服助手"}
data: {"type": "text", "content": "，很高兴为您服务！"}
data: {"type": "end"}
```

#### C.4 会话管理接口

**创建会话**
```http
POST /api/conversation
Authorization: Bearer <token>
Content-Type: application/json

Request:
{
  "title": "技术咨询",
  "current_agent_code": "tech-support"
}

Response:
{
  "code": 200,
  "message": "创建成功",
  "data": {
    "id": "conv-456",
    "title": "技术咨询",
    "user_id": 1,
    "current_agent_code": "tech-support",
    "created_at": "2024-01-01T10:00:00Z"
  }
}
```

**获取会话列表**
```http
GET /api/conversation?keyword=技术&size=10&offset=0
Authorization: Bearer <token>

Response:
{
  "code": 200,
  "message": "查询成功",
  "data": {
    "conversations": [
      {
        "id": "conv-456",
        "title": "技术咨询",
        "updated_at": "2024-01-01T10:30:00Z",
        "current_agent_code": "tech-support"
      }
    ],
    "total_count": 1
  }
}
```

#### C.5 RAGs 知识库接口

**创建知识库**
```http
POST /api/rags/knowledge-bases
Authorization: Bearer <token>
Content-Type: application/json

Request:
{
  "name": "技术文档库",
  "description": "存储技术文档和API文档",
  "embedding_model": "text-embedding-ada-002",
  "dimension": 1536,
  "chunk_size": 1024,
  "chunk_overlap": 200
}

Response:
{
  "id": "kb-789",
  "name": "技术文档库",
  "description": "存储技术文档和API文档",
  "collection_name": "kb_789",
  "embedding_model": "text-embedding-ada-002",
  "dimension": 1536,
  "create_time": "2024-01-01T10:00:00Z"
}
```

**上传文档**
```http
POST /api/rags/knowledge-bases/{kb_id}/documents
Authorization: Bearer <token>
Content-Type: multipart/form-data

Request:
- file: document.pdf
- title: "API 使用指南"
- metadata: {"category": "api", "version": "1.0"}

Response:
{
  "id": "doc-101",
  "knowledge_base_id": "kb-789",
  "title": "API 使用指南",
  "file_type": "pdf",
  "file_size": 1024000,
  "status": "processing",
  "create_time": "2024-01-01T10:05:00Z"
}
```

**知识库检索**
```http
POST /api/rags/knowledge-bases/{kb_id}/search
Authorization: Bearer <token>
Content-Type: application/json

Request:
{
  "query": "如何使用 API 进行身份验证",
  "search_type": "hybrid",
  "top_k": 5,
  "score_threshold": 0.7,
  "use_rerank": true,
  "final_top_k": 3
}

Response:
{
  "query": "如何使用 API 进行身份验证",
  "chunks": [
    {
      "id": "chunk-201",
      "document_id": "doc-101",
      "content": "API 身份验证使用 Bearer Token...",
      "score": 0.95,
      "metadata": {
        "document_title": "API 使用指南",
        "page": 5
      }
    }
  ],
  "total_count": 3,
  "search_type": "hybrid",
  "used_rerank": true
}
```

### D. 错误代码说明

#### D.1 HTTP 状态码

| 状态码 | 说明 | 示例 |
|--------|------|------|
| 200 | 成功 | 请求处理成功 |
| 400 | 请求错误 | 参数格式错误 |
| 401 | 未认证 | Token 无效或过期 |
| 403 | 权限不足 | 无访问权限 |
| 404 | 资源不存在 | 智能体不存在 |
| 500 | 服务器错误 | 内部处理异常 |

#### D.2 业务错误码

| 错误码 | 说明 | 解决方案 |
|--------|------|----------|
| 10001 | 用户未认证 | 重新登录获取 Token |
| 10002 | Token 已过期 | 刷新 Token |
| 10003 | 权限不足 | 联系管理员分配权限 |
| 20001 | 智能体不存在 | 检查 agent_code |
| 20002 | 智能体配置错误 | 检查智能体配置 |
| 30001 | 会话不存在 | 创建新会话 |
| 30002 | 消息格式错误 | 检查消息格式 |
| 40001 | 知识库不存在 | 检查知识库 ID |
| 40002 | 文档上传失败 | 检查文件格式和大小 |

### E. 性能基准测试

#### E.1 API 性能指标

| 接口 | 平均响应时间 | QPS | 并发数 |
|------|-------------|-----|--------|
| 用户登录 | 50ms | 1000 | 100 |
| 获取智能体列表 | 30ms | 2000 | 200 |
| 发起对话 | 200ms | 500 | 50 |
| 知识库检索 | 100ms | 800 | 80 |

#### E.2 系统资源使用

| 组件 | CPU 使用率 | 内存使用 | 磁盘 I/O |
|------|-----------|----------|----------|
| Agent Server | 30% | 2GB | 低 |
| PostgreSQL | 20% | 1GB | 中 |
| Redis | 10% | 512MB | 低 |
| Milvus | 40% | 4GB | 高 |

### F. 故障排除指南

#### F.1 常见问题

**问题：服务启动失败**
```bash
# 检查配置文件
python -c "
import yaml
with open('config/application.yml') as f:
    config = yaml.safe_load(f)
    print('配置文件格式正确')
"

# 检查数据库连接
python -c "
from agent_server.core.services.database import db_manager
import asyncio
asyncio.run(db_manager.init_db())
print('数据库连接正常')
"
```

**问题：智能体响应异常**
```bash
# 检查 MCP 服务器状态
python mcpserver/mcp_server.py

# 检查模型配置
curl -X GET "http://localhost:8000/api/public/agent/models"
```

**问题：知识库检索失败**
```bash
# 检查 Milvus 连接
python -c "
from agent_rags.rags.services.vector_store import create_vector_store
store = create_vector_store()
print('Milvus 连接正常')
"
```

#### F.2 日志分析

**查看应用日志**
```bash
# 查看最新日志
tail -f logs/$(date +%Y%m%d).log

# 搜索错误日志
grep "ERROR" logs/$(date +%Y%m%d).log

# 搜索特定智能体日志
grep "agent_code=customer-service" logs/$(date +%Y%m%d).log
```

**日志级别调整**
```python
# 动态调整日志级别
from agent_server.core.config.app_logger import set_log_level
set_log_level("DEBUG")
```

---

**Agent Server** - 构建下一代 AI 应用的企业级框架 🚀
