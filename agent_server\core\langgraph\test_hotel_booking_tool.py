from typing import Dict, Any
from langchain_core.tools import BaseTool, tool, StructuredTool
from agent_server.core.config.app_logger import logger

# 自定义酒店预订工具函数
@tool
async def book_hotel(
    hotel_name: str,
    check_in_date: str,
    check_out_date: str,
    guest_name: str,
    room_type: str = "standard"
) -> Dict[str, Any]:
    """
    酒店预订工具函数

    Args:
        hotel_name (str): 酒店名称
        check_in_date (str): 入住日期，格式为YYYY-MM-DD
        check_out_date (str): 退房日期，格式为YYYY-MM-DD
        guest_name (str): 客人姓名
        room_type (str, optional): 房间类型，默认为standard

    Returns:
        Dict[str, Any]: 预订结果，包含状态和消息

    Raises:
        ValueError: 如果参数无效
    """
    logger.info(f"尝试预订酒店: {hotel_name}, 入住: {check_in_date}, 退房: {check_out_date}, 客人: {guest_name}, 房型: {room_type}")

    # 模拟三种情况
    if not hotel_name or not check_in_date or not check_out_date or not guest_name:
        return {"status": "error", "message": "参数错误: 请提供完整的预订信息"}
    elif hotel_name == "NoRoomHotel":
        return {"status": "error", "message": "酒店无房"}
    else:
        return {"status": "success", "message": f"成功预订: {hotel_name}, 房型: {room_type}, 入住: {check_in_date}, 退房: {check_out_date}"}


class TestHotelBookingTool:
    """测试酒店预订工具函数的类"""

    async def test_successful_booking(self):
        """测试成功预订酒店的情况"""
        result = await book_hotel(
            hotel_name="GrandHotel",
            check_in_date="2025-09-15",
            check_out_date="2025-09-20",
            guest_name="张三"
        )
        assert result["status"] == "success"
        assert "成功预订" in result["message"]

    async def test_no_room_available(self):
        """测试酒店无房的情况"""
        result = await book_hotel(
            hotel_name="NoRoomHotel",
            check_in_date="2025-09-15",
            check_out_date="2025-09-20",
            guest_name="李四"
        )
        assert result["status"] == "error"
        assert "酒店无房" in result["message"]

    async def test_invalid_parameters(self):
        """测试参数错误的情况"""
        result = await book_hotel(
            hotel_name="",
            check_in_date="2025-09-15",
            check_out_date="2025-09-20",
            guest_name="王五"
        )
        assert result["status"] == "error"
        assert "参数错误" in result["message"]


if __name__ == "__main__":
    import asyncio

    async def run_tests():
        tester = TestHotelBookingTool()
        await tester.test_successful_booking()
        await tester.test_no_room_available()
        await tester.test_invalid_parameters()
        print("所有测试通过！")

    asyncio.run(run_tests())