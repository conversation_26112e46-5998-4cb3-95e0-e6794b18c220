#!/usr/bin/env python3
"""
PyPI 配置设置脚本

帮助用户正确设置 .pypirc 配置文件
"""

import os
import stat
import shutil
from pathlib import Path


def setup_pypirc():
    """设置 .pypirc 配置文件"""
    
    # 获取用户家目录
    home_dir = Path.home()
    pypirc_path = home_dir / ".pypirc"
    template_path = Path(".pypirc.template")
    
    print("🔧 设置 PyPI 配置文件")
    print("=" * 50)
    
    # 检查模板文件是否存在
    if not template_path.exists():
        print("❌ 模板文件 .pypirc.template 不存在")
        return False
    
    # 检查是否已存在配置文件
    if pypirc_path.exists():
        overwrite = input(f"⚠️  配置文件 {pypirc_path} 已存在，是否覆盖? (y/N): ").strip().lower()
        if overwrite not in ['y', 'yes']:
            print("❌ 取消操作")
            return False
    
    try:
        # 复制模板文件
        shutil.copy2(template_path, pypirc_path)
        print(f"✅ 已复制模板文件到: {pypirc_path}")
        
        # 设置文件权限 (只有用户可读写)
        os.chmod(pypirc_path, stat.S_IRUSR | stat.S_IWUSR)
        print("🔒 已设置文件权限为 600 (仅用户可读写)")
        
        print("\n📝 接下来的步骤:")
        print(f"1. 编辑配置文件: vim {pypirc_path}")
        print("2. 填入你的 API Token:")
        print("   - PyPI: https://pypi.org/manage/account/token/")
        print("   - Test PyPI: https://test.pypi.org/manage/account/token/")
        print("3. 保存文件并开始使用")
        
        print("\n🔑 API Token 获取方法:")
        print("1. 访问 https://pypi.org/manage/account/token/")
        print("2. 点击 'Add API token'")
        print("3. 输入 token 名称，选择作用域")
        print("4. 复制生成的 token (以 pypi- 开头)")
        print("5. 在配置文件中替换 'pypi-your-api-token-here'")
        
        return True
        
    except Exception as e:
        print(f"❌ 设置失败: {e}")
        return False


def test_config():
    """测试配置是否正确"""
    home_dir = Path.home()
    pypirc_path = home_dir / ".pypirc"
    
    if not pypirc_path.exists():
        print("❌ 配置文件不存在，请先运行设置")
        return False
    
    print("🧪 测试配置文件...")
    
    try:
        # 检查文件权限
        file_stat = pypirc_path.stat()
        file_mode = stat.filemode(file_stat.st_mode)
        
        if file_stat.st_mode & 0o077:
            print("⚠️  文件权限过于宽松，建议设置为 600")
            print(f"   当前权限: {file_mode}")
            print(f"   修复命令: chmod 600 {pypirc_path}")
        else:
            print("✅ 文件权限正确")
        
        # 读取配置文件内容
        with open(pypirc_path, 'r') as f:
            content = f.read()
        
        # 检查是否包含模板占位符
        if "your-api-token-here" in content:
            print("⚠️  配置文件包含模板占位符，请填入真实的 API Token")
        else:
            print("✅ 配置文件已更新")
        
        # 检查配置段
        sections = ["[pypi]", "[testpypi]"]
        for section in sections:
            if section in content:
                print(f"✅ 找到配置段: {section}")
            else:
                print(f"⚠️  缺少配置段: {section}")
        
        return True
        
    except Exception as e:
        print(f"❌ 测试失败: {e}")
        return False


def show_usage():
    """显示使用说明"""
    print("📚 PyPI 发布使用说明")
    print("=" * 50)
    
    print("\n1. 构建包:")
    print("   python -m build")
    
    print("\n2. 检查包:")
    print("   python -m twine check dist/*")
    
    print("\n3. 上传到测试环境:")
    print("   python -m twine upload --repository testpypi dist/*")
    
    print("\n4. 从测试环境安装:")
    print("   pip install -i https://test.pypi.org/simple/ your-package")
    
    print("\n5. 上传到生产环境:")
    print("   python -m twine upload dist/*")
    
    print("\n6. 从生产环境安装:")
    print("   pip install your-package")
    
    print("\n🔗 相关链接:")
    print("- PyPI 官网: https://pypi.org/")
    print("- Test PyPI: https://test.pypi.org/")
    print("- API Token 管理: https://pypi.org/manage/account/token/")
    print("- Twine 文档: https://twine.readthedocs.io/")


def main():
    """主函数"""
    import argparse
    
    parser = argparse.ArgumentParser(description="PyPI 配置设置工具")
    parser.add_argument("--setup", action="store_true", help="设置 .pypirc 配置文件")
    parser.add_argument("--test", action="store_true", help="测试配置文件")
    parser.add_argument("--usage", action="store_true", help="显示使用说明")
    
    args = parser.parse_args()
    
    if not any([args.setup, args.test, args.usage]):
        parser.print_help()
        return
    
    if args.setup:
        setup_pypirc()
    
    if args.test:
        test_config()
    
    if args.usage:
        show_usage()


if __name__ == "__main__":
    main()
