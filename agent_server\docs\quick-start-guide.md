# Agent Server 快速入门指南

## 🚀 5分钟快速开始

### 前置要求

- Python 3.11+
- PostgreSQL 14+
- Redis 6+

### 1. 安装项目

```bash
# 克隆项目
git clone <repository-url>
cd agent-server

# 安装依赖
pip install -r requirements.txt
# 或使用 uv (推荐)
uv sync
```

### 2. 配置环境

```bash
# 复制配置文件
cp config/application.yml.example config/application.yml

# 编辑配置文件，修改以下关键配置：
vim config/application.yml
```

**最小配置示例：**
```yaml
# 数据库配置
database:
  url: postgresql+asyncpg://postgres:password@localhost:5432/agent_db

# Redis 配置
redis:
  host: localhost
  port: 6379
  password: ""
  db: 0

# 模型配置
ai:
  models:
    default: qwen
    qwen:
      name: qwen-max
      api_key: your-api-key
      base_url: https://dashscope.aliyuncs.com/api/v1/services/aigc/text-generation/generation

# 认证配置
auth:
  adapter: agent_server.core.auth.default_auth_adapter.DefaultAuthAdapter
  auth_url: http://your-auth-service/api/user/current
```

### 3. 初始化数据库

```bash
# 创建数据库
createdb agent_db

# 初始化表结构
python -c "
from agent_server.core.services.database import db_manager
import asyncio
asyncio.run(db_manager.init_db())
print('数据库初始化完成')
"
```

### 4. 启动服务

```bash
# 开发环境启动
uvicorn main:app --reload --port 8000

# 访问 API 文档
open http://localhost:8000/docs
```

## 📖 基础使用

### 1. 用户认证

```bash
# 登录获取 Token
curl -X POST "http://localhost:8000/api/public/login" \
  -H "Content-Type: application/json" \
  -d '{"username": "admin", "password": "admin123"}'
```

### 2. 获取智能体列表

```bash
curl -X GET "http://localhost:8000/api/agent" \
  -H "Authorization: Bearer YOUR_TOKEN"
```

### 3. 发起对话

```bash
curl -X POST "http://localhost:8000/api/chat/completion" \
  -H "Authorization: Bearer YOUR_TOKEN" \
  -H "Content-Type: application/json" \
  -d '{
    "message": "你好",
    "agent_code": "customer-service",
    "conversation_id": "test-conv-001"
  }'
```

## 🔧 自定义智能体

### 1. 创建自定义发送器

```python
# core/api/my_agent_sender.py
from agent_server.core.base.base_sender import BaseSender

class MyAgentSender(BaseSender):
    async def _generate_content(self, message: str, **kwargs):
        """自定义智能体逻辑"""
        # 处理用户消息
        response = f"收到消息: {message}"
        
        # 返回流式响应
        yield {
            "type": "text",
            "content": response
        }
```

### 2. 注册智能体

在数据库中添加智能体配置：

```sql
INSERT INTO ai_agent (
    agent_code, 
    agent_name, 
    target_type, 
    agent_desc,
    disabled
) VALUES (
    'my-custom-agent',
    '我的自定义智能体',
    'LANGCHAIN',
    '这是一个自定义智能体',
    '0'
);
```

或在配置文件中添加：

```yaml
agents:
  - name: my-custom-agent
    impl: agent_server.core.api.my_agent_sender.MyAgentSender
```

## 🧠 RAGs 知识库

### 1. 启用 RAGs 模块

```yaml
# config/application.yml
rags:
  enabled: true
  vector_store:
    type: "milvus"
    milvus:
      uri: "http://localhost:19530"
  embedding:
    base_url: "http://your-proxy:3000/v1"
    api_key: "your-embedding-api-key"
    model: "text-embedding-ada-002"
```

### 2. 创建知识库

```bash
curl -X POST "http://localhost:8000/api/rags/knowledge-bases" \
  -H "Authorization: Bearer YOUR_TOKEN" \
  -H "Content-Type: application/json" \
  -d '{
    "name": "技术文档库",
    "description": "存储技术文档",
    "embedding_model": "text-embedding-ada-002"
  }'
```

### 3. 上传文档

```bash
curl -X POST "http://localhost:8000/api/rags/knowledge-bases/KB_ID/documents" \
  -H "Authorization: Bearer YOUR_TOKEN" \
  -F "file=@document.pdf" \
  -F "title=技术文档" \
  -F "metadata={\"category\": \"tech\"}"
```

### 4. 检索知识

```bash
curl -X POST "http://localhost:8000/api/rags/knowledge-bases/KB_ID/search" \
  -H "Authorization: Bearer YOUR_TOKEN" \
  -H "Content-Type: application/json" \
  -d '{
    "query": "如何部署系统",
    "search_type": "hybrid",
    "top_k": 5
  }'
```

## 🔌 MCP 工具开发

### 1. 创建自定义工具

```python
# mcpserver/mcp_server.py
@mcp.tool()
async def my_custom_tool(param1: str, param2: int = 10) -> str:
    """
    自定义工具描述
    
    Parameters:
        param1 (str): 参数1描述
        param2 (int): 参数2描述，默认值为10
    
    Returns:
        str: 处理结果
    """
    # 实现工具逻辑
    result = f"处理 {param1}，参数2: {param2}"
    return result
```

### 2. 配置 MCP 服务器

```yaml
# config/application.yml
mcp_servers:
  - key: my-mcp-server
    type: stdio
    url: mcpserver/mcp_server.py
    enabled: true
```

## 🌐 插件开发

### 1. 创建插件模块

```python
# plugins/my_plugin/api.py
from fastapi import APIRouter
from agent_server.core.api.router_registry import router_register

# 注册路由
router_register(
    prefix="/my-plugin",
    tags=["我的插件"],
    description="自定义插件API"
)

router = APIRouter()

@router.get("/hello")
async def hello():
    return {"message": "Hello from my plugin!"}

@router.post("/process")
async def process_data(data: dict):
    # 处理数据
    return {"result": "processed", "data": data}
```

### 2. 配置插件

```yaml
# config/application.yml
routers:
  auto_discovery:
    enabled: true
    packages:
      - "plugins"  # 自动发现插件
```

## 🐳 Docker 部署

### 1. 使用 Docker Compose

```yaml
# docker-compose.yml
version: '3.8'
services:
  agent-server:
    build: .
    ports:
      - "8000:8000"
    environment:
      - DATABASE_URL=********************************************/agent_db
      - REDIS_URL=redis://redis:6379
    depends_on:
      - postgres
      - redis
  
  postgres:
    image: postgres:14
    environment:
      POSTGRES_DB: agent_db
      POSTGRES_USER: postgres
      POSTGRES_PASSWORD: password
    volumes:
      - postgres_data:/var/lib/postgresql/data
  
  redis:
    image: redis:6-alpine
    volumes:
      - redis_data:/data

volumes:
  postgres_data:
  redis_data:
```

### 2. 启动服务

```bash
# 构建并启动
docker-compose up -d

# 查看日志
docker-compose logs -f agent-server

# 停止服务
docker-compose down
```

## 🔍 常见问题

### Q1: 服务启动失败

**A:** 检查配置文件格式和数据库连接：
```bash
# 验证配置文件
python -c "import yaml; yaml.safe_load(open('config/application.yml'))"

# 测试数据库连接
psql -h localhost -U postgres -d agent_db -c "SELECT 1;"
```

### Q2: 智能体无响应

**A:** 检查模型配置和 API 密钥：
```bash
# 测试模型 API
curl -X POST "YOUR_MODEL_BASE_URL/chat/completions" \
  -H "Authorization: Bearer YOUR_API_KEY" \
  -H "Content-Type: application/json" \
  -d '{"model": "gpt-3.5-turbo", "messages": [{"role": "user", "content": "test"}]}'
```

### Q3: RAGs 检索失败

**A:** 检查 Milvus 连接和嵌入模型：
```bash
# 检查 Milvus 状态
curl http://localhost:19530/health

# 测试嵌入模型
python -c "
from agent_rags.rags.services.embedding_service import EmbeddingService
service = EmbeddingService()
result = service.embed_text('test')
print('嵌入模型正常')
"
```

## 📚 更多资源

- [完整技术文档](./agent-server-technical-documentation.md)
- [API 接口文档](http://localhost:8000/docs)
- [路由注册指南](./router_registry_guide.md)
- [GitHub 项目地址](https://github.com/your-repo)

## 🤝 获取帮助

- **GitHub Issues**: 提交 Bug 报告和功能请求
- **邮箱支持**: <EMAIL>
- **技术交流**: 加入开发者社区

---

🎉 **恭喜！** 你已经成功启动了 Agent Server。开始构建你的 AI 应用吧！
