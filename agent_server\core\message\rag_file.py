from dataclasses import dataclass
import json
from typing import List
import asyncio
from uuid import uuid4

from agent_server.core.services.database import db_manager
from agent_server.core.services.database.crud.user_uploaded_files import (
    user_uploaded_files_crud,
)
from agent_server.core.config.app_logger import logger
from agent_server.core.services.database.schemas.user_uploaded_files import FileStatus


@dataclass
class FileListItem:
    id: str
    name: str
    status: FileStatus


class RAGFileMessage:
    def __init__(self, file_ids: List[str]):
        self.file_ids = file_ids
        self.file_list: List[str] = []
        self.state_key = "state_" + uuid4().hex()

    def gen_file_list(self):
        """
        根据文件 file_ids 分别查询文件的 rag_service 属性，根据 rag_service 调用对应的 RAGService 的
        get_file_status() 方法查询文件状态，并将文件状态添加到 self.file_list 列表中。
        """
        yield self._gen_init_list()
        while True:
            for id in self.file_ids:
                pass
            yield self._gen_update_state()
            asyncio.sleep(2)

    async def _gen_init_list(self):
        try:
            async with db_manager.session() as session:
                file_list = await user_uploaded_files_crud.get_by_ids(session, self.ids)
                for file in file_list:
                    newItem = {"id": file.id, "name": file.name, "status": file.status}
                    self.file_list.append(newItem)
        except Exception as e:
            logger.error(f"Error during query file list: {e}")
            raise

    def _gen_initial_message(self):
        template = f"""
<message-embedded>
{self._gen_initial_state()}
{self._gen_widget()}
</message-embedded>
"""
        return template

    def _gen_initial_state(self):
        template = f"""
<state>
  <set>
    <strategy>replace</strategy>
    <path>{self.state_key}</path>
    <value>[]</value>
  </set>
</state>
"""
        return template

    def _gen_widget(self, file: FileListItem):
        template = f"""
<widget>
    <code>@BuildIn/Files</code>
    <props>
        <data>{{{{{self.state_key}}}}}</data>
    </props>
</widget> 
"""
        return template

    def _gen_update_state(self):
        data = json.dumps(self.file_list)
        template = f"""
<message-embedded>
  <state>
    <set>
      <strategy>replace</strategy>
      <path>{self.state_key}</path>
      <value>{data}</value>
    </set>
  </state>
</message-embedded>
"""
        return template
