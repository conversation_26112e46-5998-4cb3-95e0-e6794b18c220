"""
示例插件
演示如何使用路由注册装饰器
"""

from fastapi import APIRouter
from fastapi.responses import JSONResponse

# 导入路由注册函数
try:
    from agent_server.core.api.router_registry import router_register
    # 注册路由模块
    router_register(
        prefix="/example",
        tags=["示例插件"],
        description="这是一个示例插件，演示路由注册的使用",
        priority=200
    )
except ImportError:
    # 如果无法导入，跳过注册
    pass

# 创建路由器
router = APIRouter(tags=["示例插件"])


@router.get("/hello")
async def hello():
    """示例接口"""
    return JSONResponse({
        "message": "Hello from example plugin!",
        "status": "success"
    })


@router.get("/info")
async def plugin_info():
    """插件信息"""
    return JSONResponse({
        "name": "示例插件",
        "version": "1.0.0",
        "description": "这是一个演示路由注册装饰器的示例插件",
        "author": "AI Assistant"
    })
