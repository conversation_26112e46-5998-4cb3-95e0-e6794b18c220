"""update prompt_template add updated_by

Revision ID: 1d4f9f5c3d7a
Revises: c2a46b8f66ca
Create Date: 2025-07-15 14:18:57.305242

"""
from typing import Sequence, Union

from alembic import op
import sqlalchemy as sa

import sqlmodel

# revision identifiers, used by Alembic.
revision: str = '1d4f9f5c3d7a'
down_revision: Union[str, None] = 'c2a46b8f66ca'
branch_labels: Union[str, Sequence[str], None] = None
depends_on: Union[str, Sequence[str], None] = None


def upgrade() -> None:
    """Upgrade schema."""
    # ### commands auto generated by Alembic - please adjust! ###
    op.add_column('prompt_template', sa.Column('updated_by', sqlmodel.sql.sqltypes.AutoString(), nullable=False, server_default=""))
    # ### end Alembic commands ###


def downgrade() -> None:
    """Downgrade schema."""
    # ### commands auto generated by Alembic - please adjust! ###
    op.drop_column('prompt_template', 'updated_by')
    # ### end Alembic commands ###
