from datetime import datetime, timezone
from typing import Optional
from sqlmodel import Field, SQLModel, Column, DateTime


class RecommendQuestionBase(SQLModel):
    """推荐问题基础模型"""

    agent_code: str = Field(description="智能体code")
    question: str = Field(description="问题")
    disabled: int = Field(description="是否启用 1禁用 0是启用 ")
    order: int = Field(default=0, description="排序顺序，用于列表显示（升序）")
    create_time: datetime = Field(
        default_factory=lambda: datetime.now(timezone.utc),
        sa_column=Column(DateTime(timezone=True)),
        description="创建时间"
    )
    update_time: datetime = Field(
        default_factory=lambda: datetime.now(timezone.utc),
        sa_column=Column(DateTime(timezone=True)),
        description="更新时间"
    )
    create_by: str = Field(description="创建人")
    update_by: str = Field(description="更新人")


class RecommendQuestionTable(RecommendQuestionBase, table=True):
    """数据库中的推荐问题模型"""
    
    __tablename__ = "recommend_question"
    
    id: int = Field(default=None, primary_key=True)


class RecommendQuestionCreate(RecommendQuestionBase):
    """创建推荐问题的模型"""
    


class RecommendQuestionUpdate(SQLModel):
    """更新推荐问题的模型"""

    agent_code: Optional[str] = None
    question: Optional[str] = None
    is_enable: Optional[int] = None
    order: Optional[int] = None
    updated_time: datetime = Field(
        default_factory=lambda: datetime.now(timezone.utc),
        description="更新时间"
    )
    update_by: str = Field(description="更新人")


class RecommendQuestionRead(RecommendQuestionBase):
    """API响应中的推荐问题模型"""
    
    id: int
